#!/usr/bin/env python3
"""
Test script for Word document image sizing functionality.

This test verifies that the updated fill_word_template function correctly
handles image sizing parameters to resize images in Word documents without
reducing their actual resolution.
"""

import os
import sys
import tempfile
from PIL import Image

# Add the parent directory to the path to import helpers
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from helpers import fill_word_template
from docx import Document

def create_test_image(width=800, height=600, filename="test_image.jpg"):
    """Create a test image with specified dimensions."""
    # Create a simple test image with some content
    img = Image.new('RGB', (width, height), color='lightblue')
    
    # Add some visual content to make it clear this is a test image
    from PIL import ImageDraw, ImageFont
    draw = ImageDraw.Draw(img)
    
    # Draw some text and shapes
    draw.rectangle([50, 50, width-50, height-50], outline='red', width=5)
    draw.text((100, 100), f"Test Image\n{width}x{height}", fill='black')
    draw.ellipse([width//4, height//4, 3*width//4, 3*height//4], outline='green', width=3)
    
    img.save(filename, 'JPEG', quality=95)
    return filename

def create_test_template():
    """Create a simple Word template for testing."""
    doc = Document()
    
    # Add a table with cells for testing
    table = doc.add_table(rows=3, cols=3)
    table.style = 'Table Grid'
    
    # Add some text to identify cells
    table.cell(0, 0).text = "Text Cell"
    table.cell(0, 1).text = "Image will go here"
    table.cell(1, 0).text = "Another text cell"
    
    template_path = "test_template.docx"
    doc.save(template_path)
    return template_path

def test_image_sizing():
    """Test the image sizing functionality."""
    print("Testing Word document image sizing functionality...")
    
    # Create test files
    test_image = create_test_image(1200, 900, "large_test_image.jpg")  # Large image
    template_path = create_test_template()
    
    try:
        # Test 1: Default image sizing (2.0 x 2.5 inches)
        print("\n1. Testing default image sizing...")
        data_dict_default = {
            "test_image": [test_image, 0, 0, 1]  # 4-element format
        }
        output_path_default = "test_output_default.docx"
        fill_word_template(data_dict_default, template_path, output_path_default)
        print(f"✓ Created document with default sizing: {output_path_default}")
        
        # Test 2: Custom image sizing (1.5 x 1.0 inches) using 6-element format
        print("\n2. Testing custom image sizing...")
        data_dict_custom = {
            "test_image": [test_image, 0, 1, 1, 1.5, 1.0]  # 6-element format with custom size
        }
        output_path_custom = "test_output_custom.docx"
        fill_word_template(data_dict_custom, template_path, output_path_custom)
        print(f"✓ Created document with custom sizing (1.5x1.0 inches): {output_path_custom}")
        
        # Test 3: Custom default sizing for entire document
        print("\n3. Testing custom default sizing...")
        data_dict_custom_default = {
            "test_image": [test_image, 0, 2, 1]  # 4-element format
        }
        output_path_custom_default = "test_output_custom_default.docx"
        fill_word_template(data_dict_custom_default, template_path, output_path_custom_default, 
                          default_image_width=1.0, default_image_height=1.2)
        print(f"✓ Created document with custom default sizing (1.0x1.2 inches): {output_path_custom_default}")
        
        # Test 4: Mixed text and image data
        print("\n4. Testing mixed text and image data...")
        data_dict_mixed = {
            "text_data": ["Sample Text", 0, 0, 0],
            "image_data": [test_image, 0, 0, 2, 0.8, 1.0],  # Small image
            "more_text": ["More Text", 0, 1, 0]
        }
        output_path_mixed = "test_output_mixed.docx"
        fill_word_template(data_dict_mixed, template_path, output_path_mixed)
        print(f"✓ Created document with mixed content: {output_path_mixed}")
        
        print("\n✅ All tests completed successfully!")
        print("\nGenerated files:")
        for file in [output_path_default, output_path_custom, output_path_custom_default, output_path_mixed]:
            if os.path.exists(file):
                print(f"  - {file}")
        
        print("\nTo verify the results:")
        print("1. Open the generated .docx files in Microsoft Word")
        print("2. Check that images appear at different sizes")
        print("3. Right-click on images and check 'Size and Properties'")
        print("4. Verify that the original image resolution is preserved")
        print("5. The display size should match the specified dimensions")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up test files
        for file in [test_image, template_path]:
            if os.path.exists(file):
                try:
                    os.remove(file)
                except:
                    pass

if __name__ == "__main__":
    success = test_image_sizing()
    sys.exit(0 if success else 1)
