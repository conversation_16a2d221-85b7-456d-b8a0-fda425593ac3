import google.generativeai as genai
import base64
import mimetypes
import anthropic
import os
from PIL import Image
import io
from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from pdf2image import convert_from_path
from docx.oxml import OxmlElement
from docx.oxml.ns import qn
import json
from google.cloud import vision
from typing import List
from ai_functions import *
from helpers import compress_img

BASE_DIR = os.path.abspath(os.path.dirname(__file__))
TEMPLATES_FOLDER = os.path.join(BASE_DIR, 'static', 'docx')
TEMPLATE_PATH = os.path.join(TEMPLATES_FOLDER, 'Certification Template.docx')
DESKTOP_PATH = os.path.join(os.path.expanduser('~'), 'Desktop')
GEMINI_KEY = os.environ.get('GEMINI_KEY')

def fetch_prompt(ocr_result, optional_prompt):

  prompt = r"""
Convert this image to a structured JSON. Use OCR text as reference when needed. Rules:
1. Optimize structure (e.g. convert implicit lists to bullet/numbered)
2. Headers/footers are single-row tables, use \n if there are multiple rows, please do not add more that 1 row
3. Use a two-column table setup, where the first column contains the properties, like name, id, birth date, etc, and the second column contains the actual values
4. Use strings for headings/titles/text; lists for tables/bullet points
5. Mark stamps/signatures as "(Stamp Affixed)"/"(Signature Affixed)" or (يوجد ختم), (يوجد توقيع) depending on the language of the document.
This also applies to Barcodes and QR Codes 
6. Use tables for right-aligned text (empty first column)
7. Layout the structure in the same order as it appears in the file
8. if there are multiple headings or text elements in a row, never make them a list, open a new heading tag for each of them
9. Do not add new tags at all, use tags from these options only, where (n) is the order number:
 (text(n), heading(n), table(n), header(n), footer(n), numbered_list(n), bullet_list(n), title(n))
10. Json does not allow duplicate keys, so please always add a number to each key, like this: (text1, text2, heading1, heading2, table1, table2) 
even if there one type of this element, still add a number to it
11. Please translate the json response (if the original content is in Arabic, make it English, and if it English, make it Arabic). DO NOTE PROVIDE BOTH LANGUAGES,
ONLY PROVIDE THE RESULT TRANSLATION JSON OBJECT
12. Make sure that you do not re-organize the order of the elements, keep the same order as it appears in the image, do not remove, add, or oraganize info. keep everything as is even if the order is messed up.
13. if you encounter a date, please write it like this: dd/mm/yyyy, this applies for both Gregorian and Hijri dates. Hijri dates are written like this: 29/04/1452 H, and Gregorian dates are written like this: 29/04/2023 G
14. please add a suggested name of the document to be used as file name that consists of First Name + Last Name only (if available) eg: AHMED ALOTAIBI Salary Certificate - MOHAAMMAD ALHARBI Medical Report - etc..

Return only valid JSON matching this structure:
{
"suggested_file_name": "AHMED ALOTAIBI Salary Certificate",
"header1": {"table": {"rows": [["Kingdom of Saudi Arabia\nMinistry of Health","Number:\nDate:\nAttachments:"]]}},
"title1": "Salary Certificate",
"heading1": "Heading 1",
"text1": "This is an example text",
"table1": {"rows": [["cell1", "cell2"]]},
"bullet_list1": ["item1", "item2"],
"numbered_list1": ["item1", "item2"],
"footer1": {"table": {"rows": [["string"]]}}
}

""" + optional_prompt

  example_json = """
  {
  "header": {
    "table": {
      "rows": [
        ["Document ID: 1429", "Company Name \\n Hitachi", "Date: 12/12/2023"]
      ]
    }
  },
  "title": "Sample Document Title",
  "heading": [
    "Heading 1"
  ],
  "text":"We would like to send this document for the vice rector Ahmed Khalid to request approval for our new project, please find the details
  attached below in this document. We need a response as fast as possible to be able to meet the deadline",
  "table": {
    "rows": [
      ["Name", "Age", "City", "Country"],
      ["John Doe", "30", "New York", "USA"],
      ["Jane Smith", "28", "London", "UK"]
    ]
  },
  "table": {
    "rows": [
      ["Stamp \\n (Stamp Affixed)", "Assistant Deputy Minister For Human Resources services \\n (Signature Affixed) \\n Ayad Saud Al Harthi"],
    ]
  },
  "bullet_list": [
    "First bullet point",
    "Second bullet point",
    "Third bullet point",
    "Fourth bullet point"
  ],
  "numbered_list": [
    "First numbered item",
    "Second numbered item",
    "Third numbered item",
    "Fourth numbered item"
  ],
  "heading": [
    "Heading 2"
  ],
  "text":"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
  Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate 
  ",
  "footer": {
    "table": {
      "rows": [
        ["Page 1 of 1"]
      ]
    }
  }
  }
  """

  prompt = (prompt + 
            f"""\n\nhere is the text that is returned from the OCR:
            \n{ocr_result}
            """)
  
  return prompt

def perform_ocr(image_path: str) -> List[str]:
    client = vision.ImageAnnotatorClient()

    with open(image_path, 'rb') as image_file:
        content = image_file.read()

    image_context = vision.ImageContext(language_hints=['ar', 'en'])
    image = vision.Image(content=content)
    response = client.text_detection(image=image, image_context=image_context)
    original_lines = response.full_text_annotation.text.split('\n')

    if response.error.message:
        raise Exception(f'{response.error.message}\nFor more info on error messages, check: '
                        'https://cloud.google.com/apis/design/errors')
    return original_lines

def process_file(file_path, ocr=True):
    """
    Process an image or PDF file. Optimizes the image using the compression function
    from helpers.py for consistent quality across the application.

    Args:
        file_path (str): Path to the file (image or PDF)
        ocr (bool): Whether to perform OCR on the image

    Returns:
        tuple: (optimized_image_path, original_lines) or None if multi-page PDF
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")
    
    # Get mime type
    mime_type, _ = mimetypes.guess_type(file_path)
    if mime_type is None:
        mime_type = 'application/octet-stream'
    
    # Handle PDF files
    if mime_type == 'application/pdf':
        try:
            # Convert first page of PDF to image
            images = convert_from_path(file_path, first_page=1, last_page=2)
            
            # Check if PDF has multiple pages
            if len(images) > 1:
                print("Multi-page PDF detected: to be implemented soon")
                return None
            
            # Convert single-page PDF to image
            img = images[0]
            
            # Generate output path in the same directory as the input PDF
            pdf_dir = os.path.dirname(file_path)
            pdf_name = os.path.splitext(os.path.basename(file_path))[0]
            image_path = os.path.join(pdf_dir, f"{pdf_name}_converted.jpg")
            
            # Save the converted image using helpers compression
            img.save(image_path, 'JPEG', quality=95)
            compress_img(image_path, quality=95)
            
            # Perform OCR on the converted image
            if ocr:
                original_lines = perform_ocr(image_path)
            else:
                original_lines = "No OCR Performed"
            
            # Continue processing with the saved image
            img = Image.open(image_path)
            
        except Exception as e:
            raise Exception(f"Error processing PDF: {str(e)}")
    
    # Handle image files
    elif mime_type.startswith('image/'):
        img = Image.open(file_path)
        if ocr:
            original_lines = perform_ocr(file_path)
        else:
            original_lines = "No OCR Performed"
    
    else:
        raise ValueError(f"Unsupported file type: {mime_type}")
    
    # Generate output path for optimized image
    input_dir = os.path.dirname(file_path)
    input_name = os.path.splitext(os.path.basename(file_path))[0]
    optimized_path = os.path.join(input_dir, f"{input_name}_optimized.jpg")

    # Save the image first (convert RGBA to RGB if necessary)
    if img.mode in ('RGBA', 'LA'):
        background = Image.new('RGB', img.size, (255, 255, 255))
        background.paste(img, mask=img.split()[-1])
        img = background

    # Save the image without resizing to maintain original quality
    img.save(optimized_path, 'JPEG', quality=95, optimize=True)

    # Use helpers.py compression function for consistent quality
    compress_img(optimized_path, quality=95)
    
    return optimized_path, original_lines

def translate_and_get_document_structure(image_path, original_lines, optional_prompt):
  # Process image to get base64 and media type
#   image_base64, image_media_type, ocr_result = process_file(image_path)
  
  # Fetch Prompt
  prompt = fetch_prompt(original_lines, optional_prompt)
  
  print(prompt)
  print("Image Path:" + image_path)

  result = call_openrouter("google/gemini-2.5-pro-preview", prompt, image_path)

#   result = call_gemini_v2("anthropic/claude-sonnet-4", prompt, image_path)

  print(result)

  return result

def has_arabic(text):
  """
  Check if text contains Arabic characters
  """
  return any('\u0600' <= c <= '\u06FF' for c in str(text))

def set_cell_border(cell, **kwargs):
  """
  Set cell border properties
  """
  tc = cell._tc
  tcPr = tc.get_or_add_tcPr()
  
  # Convert kwargs to xml elements
  for key, value in kwargs.items():
      tag = f'w:{key}'
      element = OxmlElement(tag)
      element.set(qn('w:val'), value)
      element.set(qn('w:sz'), '4')  # Border width
      element.set(qn('w:color'), 'D3D3D3')  # Light gray color
      tcPr.append(element)

def add_run_with_language(paragraph, text, rtl=False):
  """
  Add a run with proper language and direction settings
  """
  run = paragraph.add_run(text)
  
  # Set RTL if needed
  if rtl:
      run._element.get_or_add_rPr().append(
          OxmlElement('w:rtl')
      )
      
      # Set Arabic language
      lang = OxmlElement('w:lang')
      lang.set(qn('w:bidi'), 'ar-SA')  # Arabic language code
      run._element.get_or_add_rPr().append(lang)
  
  return run

def add_paragraph_with_language(doc, text, alignment=None, style=None, is_bold=False):
  """
  Add a paragraph with proper language and direction settings
  """
  paragraph = doc.add_paragraph()
  paragraph.paragraph_format.space_before = Pt(0)
  paragraph.paragraph_format.space_after = Pt(0)
  
  if alignment:
      paragraph.alignment = alignment
  
  if style:
      paragraph.style = style
  
  if has_arabic(text):
      # Set paragraph direction to RTL
      paragraph._element.get_or_add_pPr().append(
          OxmlElement('w:bidi')
      )
      run = add_run_with_language(paragraph, text, rtl=True)
  else:
      run = paragraph.add_run(text)
  
  if is_bold:
      run.bold = True
  
  return paragraph

def add_table_to_doc(doc, rows, is_header=False):
  """
  Add a table to the document with proper formatting, handling irregular rows
  """
  if not rows:
      return
  
  max_cols = max(len(row) for row in rows)
  table = doc.add_table(rows=len(rows), cols=max_cols)
  
  # Apply light gray borders to all cells
  for row in table.rows:
      for cell in row.cells:
          set_cell_border(
              cell,
              top={"val": "single", "color": "D3D3D3"},
              bottom={"val": "single", "color": "D3D3D3"},
              start={"val": "single", "color": "D3D3D3"},
              end={"val": "single", "color": "D3D3D3"}
          )
  
  # Fill table content and apply formatting
  for i, row in enumerate(rows):
      for j, cell_content in enumerate(row):
          cell = table.cell(i, j)
          paragraph = cell.paragraphs[0]
          paragraph.paragraph_format.space_before = Pt(0)
          paragraph.paragraph_format.space_after = Pt(0)
          
          # Check if content contains Arabic text
          has_arabic = any('\u0600' <= c <= '\u06FF' for c in str(cell_content))
          
          # Handle multiple lines in cell content
          lines = str(cell_content).split('\n')
          if has_arabic:
              # Set paragraph direction to RTL
              paragraph._element.get_or_add_pPr().append(
                  OxmlElement('w:bidi')
              )
              add_run_with_language(paragraph, lines[0], rtl=True)
              for line in lines[1:]:
                  paragraph.add_run('\n')
                  add_run_with_language(paragraph, line, rtl=True)
          else:
              paragraph.text = lines[0]
              for line in lines[1:]:
                  paragraph.add_run('\n' + line)
      
      # Handle merging cells if needed
      if len(row) < max_cols and len(row) > 0:
          if max_cols - len(row) > 0:
              start_cell = table.cell(i, len(row) - 1)
              end_cell = table.cell(i, max_cols - 1)
              if start_cell != end_cell:
                  start_cell.merge(end_cell)
  
  return table

def set_cell_border(cell, **kwargs):
    """
    Set cell border
    Usage:
    set_cell_border(
        cell,
        top={"val": "single", "color": "D3D3D3"},
        bottom={"val": "single", "color": "D3D3D3"},
        start={"val": "single", "color": "D3D3D3"},
        end={"val": "single", "color": "D3D3D3"},
    )
    """
    tc = cell._tc
    tcPr = tc.get_or_add_tcPr()

    # check for tag existence, if none found, then create one
    tcBorders = tcPr.first_child_found_in("w:tcBorders")
    if tcBorders is None:
        tcBorders = OxmlElement('w:tcBorders')
        tcPr.append(tcBorders)

    # list over all available tags
    for edge in ('start', 'top', 'end', 'bottom', 'insideH', 'insideV'):
        edge_data = kwargs.get(edge)
        if edge_data:
            tag = 'w:{}'.format(edge)

            # check for tag existence, if none found, then create one
            element = tcBorders.find(qn(tag))
            if element is None:
                element = OxmlElement(tag)
                tcBorders.append(element)

            # set the border properties
            for key in ["val", "color", "sz", "space", "shadow"]:
                if key in edge_data:
                    element.set(qn('w:{}'.format(key)), str(edge_data[key]))

def convert_strucuture_into_word_file(document_json_structure):
  # Clean and parse JSON string into dictionary
#   document_json_structure = document_json_structure.strip()
#   document_json_structure = json.loads(document_json_structure)
  
  # Use template and preserve existing content
  doc = Document(TEMPLATE_PATH)
  
  # Add Arabic language support to the document defaults
  settings = doc.settings._element
  bidi = OxmlElement('w:bidi')
  settings.append(bidi)
  
  # Process elements in order as they appear in the JSON
  for key, value in document_json_structure.items():
      # Add a regular line break instead of paragraph spacing
      if key != list(document_json_structure.keys())[0]:
          paragraph = doc.add_paragraph()
          paragraph.paragraph_format.space_before = Pt(0)
          paragraph.paragraph_format.space_after = Pt(0)
          
      if 'header' in key and isinstance(value, dict) and 'table' in value:
          add_table_to_doc(doc, value['table']['rows'], True)
          
      elif 'title' in key:
          add_paragraph_with_language(
              doc, 
              value, 
              alignment=WD_ALIGN_PARAGRAPH.CENTER, 
              is_bold=True
          )
          
      elif 'heading' in key:
          heading_level = 1
          heading_text = value
          if isinstance(value, dict):
              heading_level = value.get('level', 1)
              heading_text = value.get('text', '')
          
          add_paragraph_with_language(
              doc,
              heading_text,
              style=f'Heading {heading_level}'
          )
          
      elif 'table' in key and isinstance(value, dict) and 'rows' in value:
          add_table_to_doc(doc, value['rows'])
          
      elif 'text' in key:
          add_paragraph_with_language(doc, value)
          
      elif 'numbered_list' in key:
          items = value if isinstance(value, list) else [value]
          for i, item in enumerate(items, 1):
              paragraph = add_paragraph_with_language(doc, f"{i}. {str(item)}")
              paragraph.paragraph_format.left_indent = Pt(36)
          
      elif 'bullet_list' in key:
          items = value if isinstance(value, list) else [value]
          for item in items:
              paragraph = add_paragraph_with_language(doc, f"• {str(item)}")
              paragraph.paragraph_format.left_indent = Pt(36)
          
      elif 'footer' in key and isinstance(value, dict) and 'table' in value:
          add_table_to_doc(doc, value['table']['rows'], True)
  
  # Save the document
  output_path = os.path.join(DESKTOP_PATH, f'{document_json_structure["suggested_file_name"]}.docx')
  doc.save(output_path)
  
  # Print the created file path
  print(f"Created file: {output_path}")

def translate_custom_file(image_path, optional_prompt=""):

    optimized_image_path, original_lines = process_file(image_path)

    ai_response = translate_and_get_document_structure(optimized_image_path, original_lines, optional_prompt)

    convert_strucuture_into_word_file(ai_response)
    '''
    1. Determine if the file is an image or pdf
    2. if it is an image, translate it normally
    3. if it is a pdt, translate and get the structure for each page, 
    then add all the structures to form a single structure for all the pages, 
    while keeping the header for the first page only, and the footer for the last page only
    4. Use the structure to generate a word document with the required elements
    5. Save the translated document to the desktop
    '''

    #     ai_response = r"""
    # {
    #   "header1": {
    #     "table": {
    #       "rows": [["Ministry of Health", "(Stamp Affixed)"]]
    #     }
    #   },
    #   "table1": {
    #     "rows": [
    #       ["Patient Name: Musaed Saleh Lafee", "Patient ID: 155939"],
    #       ["Arabic Name:", "National ID: **********"],
    #       ["Date Of Birth: 12/24/1994", "Accession: **********"],
    #       ["Gender: M", "Age: 29Y 9M"],
    #       ["Procedure: CT WHOLE SPINE", "Ref. Physician:"]
    #     ]
    #   },
    #   "heading1": "PROCEDURE:",
    #   "heading2": "CLINICAL INDICATION: TRAUMA",
    #   "heading3": "TECHNIQUE:",
    #   "heading4": "COMPARISON:",
    #   "heading5": "FINDINGS:",
    #   "heading6": "CT LUMBOSACRAL SPINE (D10-SACRUM) IN AXIAL CUTS, CORONAL AND SAGITTAL RECONSTRUCTED IMAGES:",
    #   "bullet_list1": [
    #     "No evidence of obvious fracture could be seen.",
    #     "Straightening of the cervical and lumbar lordosis, denoting posterior neck and back muscles spasm seen.",
    #     "Normal alignment of cervico-dorsal spine with no signs of instability seen.",
    #     "Normal sagittal diameter of the bony lumbar spinal canal with no evidence of spinal canal or lateral recesses stenosis.",
    #     "No CT evidence of disc lesions.",
    #     "D11/12 disc degeneration seen.",
    #     "A small Schmorl's node is seen along D12 superior VEP, denoting intervertebral vertical disc herniation.",
    #     "Otherwise, the osseous features of the vertebral bodies and their appendages appear normal.",
    #     "Normal CT appearance of ligamenta flava.",
    #     "No evidence of facet arthropathy.",
    #     "Normal CT appearance of both sacroiliac joints.",
    #     "No retro- or paraspinal abnormal soft tissue shadows seen."
    #   ],
    #   "heading7": "Impression:",
    #   "text1": "D11/12 disc degeneration"
    # }
    #   """

    return ai_response
