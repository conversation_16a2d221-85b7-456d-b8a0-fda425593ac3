# Word Document Image Sizing Feature

## Overview

The `fill_word_template` function in `helpers.py` now supports resizing images in Word documents without reducing their actual resolution. This is similar to manually resizing images in Microsoft Word - the display size changes but the original image quality is preserved.

## How It Works

When you resize an image in Microsoft Word by dragging its corners or using the Size dialog, Word doesn't actually change the image file itself. Instead, it changes how the image is displayed within the document. Our implementation does exactly the same thing programmatically.

## Usage

### Method 1: Using Default Image Sizes

```python
# Standard 4-element format - uses default image dimensions (2.0 x 2.5 inches)
data_dict = {
    "my_image": [image_path, table_index, row, col]
}

fill_word_template(data_dict, template_path, output_path)
```

### Method 2: Custom Image Sizes Per Image

```python
# Extended 6-element format - specify custom dimensions for each image
data_dict = {
    "small_image": [image_path, table_index, row, col, 1.0, 1.2],  # 1.0 x 1.2 inches
    "large_image": [image_path, table_index, row, col, 3.0, 4.0],  # 3.0 x 4.0 inches
    "text_data": ["Some text", table_index, row, col]  # Text data (4-element format)
}

fill_word_template(data_dict, template_path, output_path)
```

### Method 3: Custom Default Sizes for All Images

```python
# Set custom default dimensions for all images in the document
data_dict = {
    "image1": [image_path1, 0, 0, 1],  # Will use 1.5 x 2.0 inches
    "image2": [image_path2, 0, 1, 1],  # Will use 1.5 x 2.0 inches
}

fill_word_template(data_dict, template_path, output_path, 
                  default_image_width=1.5, default_image_height=2.0)
```

## Data Dictionary Format

### 4-Element Format (Standard)
```python
[data, table_index, start_row, start_col]
```
- Uses default image dimensions or function parameters
- Compatible with existing code

### 6-Element Format (Extended)
```python
[data, table_index, start_row, start_col, image_width_inches, image_height_inches]
```
- Allows custom dimensions per image
- Only applies to image data (ignored for text)

## Function Parameters

```python
def fill_word_template(data_dict, template_path, output_path, 
                      default_image_width=2.0, default_image_height=2.5):
```

- `data_dict`: Dictionary containing the data to insert
- `template_path`: Path to the Word template file
- `output_path`: Path where the output document will be saved
- `default_image_width`: Default width in inches for images (default: 2.0)
- `default_image_height`: Default height in inches for images (default: 2.5)

## Real-World Examples

### ID Document with Person Photo
```python
# Person photo should be smaller than default
word_ready_dict = {
    "name": ["John Doe", 1, 0, 0],
    "person_image": [photo_path, 2, 0, 0, 1.2, 1.6],  # ID photo size: 1.2x1.6 inches
    "id_number": ["123456789", 1, 1, 1],
}
```

### Family Card with Multiple Photos
```python
word_ready_dict = {
    "family_head_photo": [head_photo_path, 0, 0, 0, 1.5, 2.0],    # Medium size
    "spouse_photo": [spouse_photo_path, 0, 0, 1, 1.5, 2.0],       # Medium size  
    "child_photo": [child_photo_path, 0, 1, 0, 1.0, 1.3],         # Smaller size
}
```

### Document with Large Images
```python
# For documents that need larger images but don't want to reduce quality
word_ready_dict = {
    "certificate_image": [cert_path, 0, 0, 0, 4.0, 3.0],  # Large display size
    "signature": [sig_path, 1, 0, 0, 2.0, 1.0],           # Wide signature
}
```

## Benefits

1. **Preserves Image Quality**: Original image resolution and quality remain unchanged
2. **Flexible Sizing**: Different images can have different display sizes
3. **Backward Compatible**: Existing code continues to work without changes
4. **Professional Layout**: Better control over document appearance
5. **File Size Optimization**: Large images don't make documents unnecessarily large when displayed small

## Technical Details

- Uses the `python-docx` library's `add_picture()` method with `width` and `height` parameters
- Dimensions are specified in inches using `docx.shared.Inches()`
- Images are automatically centered in their table cells
- Original image files are never modified
- Works with all common image formats (JPG, PNG, BMP, etc.)

## Testing

Run the test script to verify functionality:

```bash
cd tests
python test_word_image_sizing.py
```

This will create sample Word documents with different image sizes that you can open and verify in Microsoft Word.

## Migration Guide

### Existing Code (No Changes Needed)
```python
# This continues to work exactly as before
data_dict = {"image": [path, 0, 0, 0]}
fill_word_template(data_dict, template, output)
```

### Enhanced Code (Optional Improvements)
```python
# Add custom sizing for better layout
data_dict = {"image": [path, 0, 0, 0, 1.5, 2.0]}  # Custom size
fill_word_template(data_dict, template, output)
```

## Troubleshooting

- **Images appear too large**: Reduce the width/height values
- **Images appear too small**: Increase the width/height values  
- **Images look pixelated**: Check that original image resolution is sufficient for the display size
- **Function not found**: Ensure you're using the updated `helpers.py` file
