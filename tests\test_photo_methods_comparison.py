#!/usr/bin/env python3
"""
Comprehensive test script to compare all three photo cropping methods.
This script tests each method individually and provides detailed comparison results.
"""

import os
import sys
from helpers import (
    crop_personal_photo_method1_face_detection,
    crop_personal_photo_method2_contour_detection,
    crop_personal_photo_method3_template_based
)

def test_all_methods_on_image(image_path):
    """Test all three methods on a single image"""
    
    if not os.path.exists(image_path):
        print(f"Image not found: {image_path}")
        return None
    
    base_name = os.path.splitext(os.path.basename(image_path))[0]
    print(f"\n=== Testing {base_name} ===")
    
    results = {}
    
    # Test Method 1: Face Detection
    print("\n--- Method 1: Face Detection ---")
    result1 = crop_personal_photo_method1_face_detection(image_path, debugging=True)
    results["face_detection"] = result1
    if result1["success"]:
        print(f"✓ SUCCESS: {result1['details']}")
        print(f"  Output: {result1['path']}")
        print(f"  Faces detected: {result1['faces_detected']}")
        print(f"  Bounding box: {result1['bbox']}")
    else:
        print(f"✗ FAILED: {result1['error']}")
    
    # Test Method 2: Contour Detection
    print("\n--- Method 2: Contour Detection ---")
    result2 = crop_personal_photo_method2_contour_detection(image_path, debugging=True)
    results["contour_detection"] = result2
    if result2["success"]:
        print(f"✓ SUCCESS: {result2['details']}")
        print(f"  Output: {result2['path']}")
        print(f"  Candidates found: {result2['candidates_found']}")
        print(f"  Bounding box: {result2['bbox']}")
    else:
        print(f"✗ FAILED: {result2['error']}")
    
    # Test Method 3: Template Based
    print("\n--- Method 3: Template Based ---")
    result3 = crop_personal_photo_method3_template_based(image_path, debugging=True)
    results["template_based"] = result3
    if result3["success"]:
        print(f"✓ SUCCESS: {result3['details']}")
        print(f"  Output: {result3['path']}")
        print(f"  Bounding box: {result3['bbox']}")
    else:
        print(f"✗ FAILED: {result3['error']}")
    
    return results

def compare_methods_performance():
    """Compare all methods on test images"""
    
    # Test with images from test_files folder
    test_images = [
        "test_files/ID1.png",
        "test_files/ID2.png", 
        "test_files/Family Card Single Page.png"
    ]
    
    # Filter to only existing files
    existing_images = [img for img in test_images if os.path.exists(img)]
    
    if not existing_images:
        print("No test images found in test_files folder")
        return
    
    print(f"Comparing photo cropping methods on {len(existing_images)} images:")
    for img in existing_images:
        print(f"  - {img}")
    
    # Create output directory for method comparison
    output_dir = "method_comparison"
    os.makedirs(output_dir, exist_ok=True)
    
    all_results = {}
    method_stats = {
        "face_detection": {"success": 0, "total": 0},
        "contour_detection": {"success": 0, "total": 0},
        "template_based": {"success": 0, "total": 0}
    }
    
    for image_path in existing_images:
        results = test_all_methods_on_image(image_path)
        if results:
            all_results[image_path] = results
            
            # Update statistics
            for method, result in results.items():
                method_stats[method]["total"] += 1
                if result["success"]:
                    method_stats[method]["success"] += 1
    
    # Print comprehensive comparison
    print(f"\n{'='*60}")
    print("COMPREHENSIVE METHOD COMPARISON")
    print(f"{'='*60}")
    
    print(f"\n{'Method':<20} {'Success Rate':<15} {'Details'}")
    print("-" * 60)
    
    for method, stats in method_stats.items():
        success_rate = (stats["success"] / stats["total"]) * 100 if stats["total"] > 0 else 0
        method_name = method.replace("_", " ").title()
        print(f"{method_name:<20} {success_rate:>6.1f}% ({stats['success']}/{stats['total']})")
    
    # Detailed analysis per image
    print(f"\n{'='*60}")
    print("DETAILED RESULTS PER IMAGE")
    print(f"{'='*60}")
    
    for image_path, results in all_results.items():
        image_name = os.path.basename(image_path)
        print(f"\n{image_name}:")
        
        for method, result in results.items():
            method_name = method.replace("_", " ").title()
            status = "✓" if result["success"] else "✗"
            print(f"  {status} {method_name:<18}: {result.get('details', result.get('error', 'Unknown'))}")
    
    # Recommendations
    print(f"\n{'='*60}")
    print("RECOMMENDATIONS")
    print(f"{'='*60}")
    
    # Find best performing method
    best_method = max(method_stats.items(), key=lambda x: x[1]["success"])
    best_method_name = best_method[0].replace("_", " ").title()
    best_success_rate = (best_method[1]["success"] / best_method[1]["total"]) * 100
    
    print(f"\n🏆 BEST PERFORMING METHOD: {best_method_name}")
    print(f"   Success Rate: {best_success_rate:.1f}%")
    
    if best_method[0] == "face_detection":
        print(f"   ✓ Most accurate for detecting actual faces")
        print(f"   ✓ Automatically adjusts to face size and position")
        print(f"   ✓ Includes proper padding around detected faces")
        print(f"   ⚠ Requires clear, frontal face visibility")
    elif best_method[0] == "contour_detection":
        print(f"   ✓ Good for detecting photo boundaries")
        print(f"   ✓ Works when face detection fails")
        print(f"   ⚠ May detect other rectangular regions")
    else:  # template_based
        print(f"   ✓ Consistent positioning for Saudi ID cards")
        print(f"   ✓ Always works as fallback method")
        print(f"   ⚠ Fixed positioning may not suit all card layouts")
    
    print(f"\n💡 RECOMMENDATION FOR PRODUCTION:")
    print(f"   Use Face Detection as primary method with Template Based as fallback")
    print(f"   This provides the best balance of accuracy and reliability.")
    
    print(f"\n📁 All test outputs saved in: {output_dir}")
    print(f"   You can visually inspect the cropped photos to verify quality.")
    
    return all_results

def main():
    """Main function"""
    
    if len(sys.argv) > 1:
        # Test single image provided as command line argument
        image_path = sys.argv[1]
        test_all_methods_on_image(image_path)
    else:
        # Compare all methods on test images
        compare_methods_performance()

if __name__ == "__main__":
    main()
