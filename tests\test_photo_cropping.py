#!/usr/bin/env python3
"""
Test script for the personal photo cropping function.
This script tests the crop_personal_photo_from_card function with images from the test_files folder.
"""

import os
import sys
from helpers import crop_personal_photo_from_card

def test_photo_cropping():
    """Test the personal photo cropping function"""
    
    # Test with images from test_files folder
    test_images = [
        "test_files/ID1.png",
        "test_files/ID2.png", 
        "test_files/Family Card Single Page.png"
    ]
    
    # Filter to only existing files
    existing_images = [img for img in test_images if os.path.exists(img)]
    
    if not existing_images:
        print("No test images found in test_files folder")
        return
    
    print(f"Testing personal photo cropping with {len(existing_images)} images:")
    for img in existing_images:
        print(f"  - {img}")
    
    # Create output directory
    output_dir = "cropped_photos"
    os.makedirs(output_dir, exist_ok=True)
    
    results = []
    
    for image_path in existing_images:
        print(f"\n=== Processing {os.path.basename(image_path)} ===")
        
        try:
            # Generate output path
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            output_path = os.path.join(output_dir, f"{base_name}_personal_photo.jpg")
            
            # Test the photo cropping function
            result_path = crop_personal_photo_from_card(
                image_path, 
                output_path=output_path, 
                debugging=True
            )
            
            if result_path and os.path.exists(result_path):
                print(f"✓ Successfully cropped personal photo: {result_path}")
                results.append(result_path)
            else:
                print(f"✗ Failed to crop personal photo from {image_path}")
                
        except Exception as e:
            print(f"✗ Error processing {image_path}: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n=== Summary ===")
    print(f"Successfully processed {len(results)} out of {len(existing_images)} images:")
    for result in results:
        print(f"  - {result}")
    
    if results:
        print(f"\nCropped photos saved in: {output_dir}")
        print("You can view the cropped photos to verify the results.")
    
    return results

def test_single_image(image_path):
    """Test photo cropping on a single image"""
    
    if not os.path.exists(image_path):
        print(f"Image not found: {image_path}")
        return None
    
    print(f"Testing photo cropping on: {image_path}")
    
    try:
        result_path = crop_personal_photo_from_card(image_path, debugging=True)
        
        if result_path and os.path.exists(result_path):
            print(f"✓ Successfully cropped personal photo: {result_path}")
            return result_path
        else:
            print(f"✗ Failed to crop personal photo")
            return None
            
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Main function"""
    
    if len(sys.argv) > 1:
        # Test single image provided as command line argument
        image_path = sys.argv[1]
        test_single_image(image_path)
    else:
        # Test all images in test_files folder
        test_photo_cropping()

if __name__ == "__main__":
    main()
