#!/usr/bin/env python3
"""
Test script for aspect ratio preservation in Word document image sizing.

This test verifies that images maintain their original aspect ratio when
resized in Word documents, regardless of the specified target dimensions.
"""

import os
import sys
import tempfile
from PIL import Image

# Add the parent directory to the path to import helpers
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from helpers import fill_word_template
from docx import Document

def create_test_images():
    """Create test images with different aspect ratios."""
    images = {}
    
    # Square image (1:1 ratio)
    img_square = Image.new('RGB', (400, 400), color='red')
    img_square.save('test_square.jpg', 'JPEG', quality=95)
    images['square'] = ('test_square.jpg', 1.0)
    
    # Wide image (2:1 ratio)
    img_wide = Image.new('RGB', (800, 400), color='blue')
    img_wide.save('test_wide.jpg', 'JPEG', quality=95)
    images['wide'] = ('test_wide.jpg', 2.0)
    
    # Tall image (1:2 ratio)
    img_tall = Image.new('RGB', (400, 800), color='green')
    img_tall.save('test_tall.jpg', 'JPEG', quality=95)
    images['tall'] = ('test_tall.jpg', 0.5)
    
    # Very wide image (3:1 ratio)
    img_very_wide = Image.new('RGB', (1200, 400), color='orange')
    img_very_wide.save('test_very_wide.jpg', 'JPEG', quality=95)
    images['very_wide'] = ('test_very_wide.jpg', 3.0)
    
    # Very tall image (1:3 ratio)
    img_very_tall = Image.new('RGB', (400, 1200), color='purple')
    img_very_tall.save('test_very_tall.jpg', 'JPEG', quality=95)
    images['very_tall'] = ('test_very_tall.jpg', 1/3)
    
    return images

def create_test_template():
    """Create a Word template with multiple cells for testing."""
    doc = Document()
    
    # Add a table with enough cells for all test images
    table = doc.add_table(rows=6, cols=3)
    table.style = 'Table Grid'
    
    # Add headers
    table.cell(0, 0).text = "Image Type"
    table.cell(0, 1).text = "Original Ratio"
    table.cell(0, 2).text = "Image"
    
    template_path = "test_aspect_ratio_template.docx"
    doc.save(template_path)
    return template_path

def test_aspect_ratio_preservation():
    """Test that aspect ratios are preserved when resizing images."""
    print("Testing aspect ratio preservation in Word document images...")
    
    # Create test images and template
    test_images = create_test_images()
    template_path = create_test_template()
    
    try:
        # Test with same target dimensions for all images (2.0 x 1.5 inches)
        # This should result in different actual dimensions to preserve aspect ratios
        print("\n1. Testing with uniform target dimensions (2.0 x 1.5 inches)...")
        
        data_dict = {}
        row = 1
        
        for img_type, (img_path, expected_ratio) in test_images.items():
            # Add image type label
            data_dict[f"label_{img_type}"] = [img_type.replace('_', ' ').title(), 0, row, 0]
            
            # Add expected ratio
            data_dict[f"ratio_{img_type}"] = [f"{expected_ratio:.2f}:1", 0, row, 1]
            
            # Add image with same target dimensions for all
            data_dict[f"image_{img_type}"] = [img_path, 0, row, 2, 2.0, 1.5]
            
            row += 1
        
        output_path = "test_aspect_ratio_output.docx"
        fill_word_template(data_dict, template_path, output_path)
        print(f"✓ Created document: {output_path}")
        
        # Test with different target dimensions
        print("\n2. Testing with varied target dimensions...")
        
        data_dict_varied = {
            "square_small": [test_images['square'][0], 0, 1, 2, 1.0, 1.0],      # Square target
            "wide_constrained": [test_images['wide'][0], 0, 2, 2, 2.0, 1.0],    # Wide target
            "tall_constrained": [test_images['tall'][0], 0, 3, 2, 1.0, 2.0],    # Tall target
            "very_wide_small": [test_images['very_wide'][0], 0, 4, 2, 1.5, 0.5], # Small target
            "very_tall_small": [test_images['very_tall'][0], 0, 5, 2, 0.5, 1.5], # Small target
        }
        
        output_path_varied = "test_aspect_ratio_varied.docx"
        fill_word_template(data_dict_varied, template_path, output_path_varied)
        print(f"✓ Created document with varied dimensions: {output_path_varied}")
        
        print("\n✅ Aspect ratio preservation tests completed successfully!")
        print("\nGenerated files:")
        for file in [output_path, output_path_varied]:
            if os.path.exists(file):
                print(f"  - {file}")
        
        print("\nTo verify the results:")
        print("1. Open the generated .docx files in Microsoft Word")
        print("2. Check that all images maintain their original proportions")
        print("3. Square images should appear square")
        print("4. Wide images should appear wide (not stretched vertically)")
        print("5. Tall images should appear tall (not stretched horizontally)")
        print("6. Right-click images and check 'Size and Properties' to see actual dimensions")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up test files
        cleanup_files = [template_path] + [img_path for img_path, _ in test_images.values()]
        for file in cleanup_files:
            if os.path.exists(file):
                try:
                    os.remove(file)
                except:
                    pass

if __name__ == "__main__":
    success = test_aspect_ratio_preservation()
    sys.exit(0 if success else 1)
