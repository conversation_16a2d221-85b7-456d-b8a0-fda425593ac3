# Test Files Directory

This directory contains all test files and test outputs created during development and testing of the photo cropping and image processing features.

## Test Scripts

### Photo Cropping Tests
- **test_photo_methods_comparison.py** - Compares different photo cropping methods (face detection, contour detection, template-based)
- **test_photo_cropping.py** - Basic photo cropping functionality tests
- **test_rotation_debug.py** - Tests for debugging image rotation issues
- **test_rotation_fix.py** - Tests for fixing image rotation problems
- **test_optimization.py** - Tests for image optimization and compression

## Test Output Files

### Method Comparison Results
- **\*_method1_face.jpg** - Results from face detection method
- **\*_method2_contour.jpg** - Results from contour detection method  
- **\*_method3_template.jpg** - Results from template-based method

### Directories
- **cropped_photos/** - Contains cropped personal photos from various test images
- **method_comparison/** - Contains detailed comparison results between different cropping methods
- **test_output/** - General test output files

## Usage

These test files were used to:
1. Compare the effectiveness of different photo cropping algorithms
2. Debug and fix image rotation issues
3. Test image optimization and compression functionality
4. Validate the photo cropping feature with various ID card formats

## Note

These files are for development and testing purposes only. The main application uses the simplified face detection method implemented in `helpers.py`.
