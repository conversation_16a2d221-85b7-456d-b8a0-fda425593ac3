from flask import Flask, render_template, request, redirect, url_for, send_file, jsonify
from helpers import *
from certificaition import *
from file_translation import translate_custom_file
import PIL, re, json, os, anthropic, base64, platform, google.generativeai as genai, urllib.parse


app = Flask(__name__)
# Get the current directory where your Flask app is located
BASE_DIR = os.path.abspath(os.path.dirname(__file__))
UPLOAD_FOLDER = os.path.join(BASE_DIR, 'uploads')
TEMPLATES_FOLDER = os.path.join(BASE_DIR, 'static', 'docx')
TRADEMARKS_TEMPLATES_FOLDER = os.path.join(BASE_DIR, 'static', 'Example Trademarks')
TEMP_DIR = os.path.join(BASE_DIR, 'static', 'temp')
DESKTOP_PATH = os.path.join(os.path.expanduser('~'), 'Desktop')

os.makedirs(TEMP_DIR, exist_ok=True)

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.secret_key = 'seCurE-rANdOm-VeLUE'  # Set this to a secure random value

document_translation_provider = "google"
tm_translation_provider = "openrouter"

default_document_model = "gemini-2.5-flash-preview-05-20"
default_tm_model_google = "gemini-2.5-flash-preview-05-20"
default_openrouter_model = "anthropic/claude-sonnet-4"


fallback_models = ["google/gemini-2.5-pro-preview-03-25", "google/gemini-2.5-flash-preview"]

def get_model_settings():
    """Return a dictionary with all model settings"""
    return {
        'document_translation_provider': document_translation_provider,
        'tm_translation_provider': tm_translation_provider,
        'default_openrouter_model': default_openrouter_model
    }

def translate_file(file_name, file_type, required_info, optional_prompt=""):
    result = {}
    result['ocr_lines'] = []
    result['original_lines'] = []
    result['ai_response'] = ""
    result['translated_data'] = {}
    result['word_ready_dict'] = {}

    ocr_lines, result['original_lines'] = perform_ocr(file_name)
    print("The Card Type is:" + file_type)
    prompt = fetch_ai_prompt(file_type, ocr_lines, required_info)
    prompt = prompt + optional_prompt
    print(prompt)

    if document_translation_provider == "google":
        ai_response = call_gemini_v2(default_document_model, prompt)
        # print(ai_response)

    elif document_translation_provider == "openrouter":
        ai_response = call_openrouter(default_openrouter_model, prompt, fallback_models)
        # print(ai_response)

    else:
        print("I don't know who this provider is? Check the AI provider name")

    result['translated_data'] = ai_response
    result['ai_response'] = ai_response
    result['ocr_lines'] = ocr_lines

    return result

def handle_family_card_translation(family_card_file_name, card_type="one-page", optional_prompt=""):

    if card_type == "one-page" or card_type == "two-pages":
        required_info = """
            "family_head_name":"ALDOSARI, MOHAMED AHMED K",
            "family_head_shortened_name":"MOHAMED ALDOSARI",
            "family_head_id":"**********",
            "card_version": "3",
            "spouse_name":"ALAHMADI, SARAH ABDULLAH S",
            "spouse_relavance": "Wife",
            "spouse_id": "**********",
            "spouse_birth_place": "Jeddah",
            "spouse_birth_date": "29/04/1419",
            "children_list": [
            {"child_name":"FARIS", "child_relavance":"Son", "child_id":"**********","child_birth_place":"Al-Madinah Al-Munawwarah","child_birth_date":"27/12/1393"},
            {"child_name":"NOOR", "child_relavance":"Daughter", "child_id":"**********","child_birth_place":"Al-Madinah Al-Munawwarah","child_birth_date":"11/11/1400"},
            {"child_name":"SHAHAD", "child_relavance":"Daughter", "child_id":"**********","child_birth_place":"Riyadh","child_birth_date":"15/02/1412"},
            etc.. (Note: Do not add a comma after the last child object)
            ],
            "issue_authority":"Riyadh Civil Affairs",
            "date_of_issue":"07/06/1444"
        """
    else:
        required_info = """
            "family_head_name":"ALDOSARI, MOHAMED AHMED K ",
            "family_head_shortened_name":"SALEH ALGHAMDI",
            "family_head_id":"**********",
            "card_version": "3",
            "date_of_issue":"07/06/1444"
            "issue_authority":"Riyadh Civil Affairs",
            "family_members_count":"3",
            "spouse_name":"ALAHMADI, SARAH ABDULLAH S",
            "spouse_relavance":"Wife",
            "spouse_id": "**********",
            "spouse_birth_place": "Jeddah",
            "spouse_birth_date": "29/04/1419",
            "children_list": [
            {"child_name":"Faris", "child_id":"**********", "child_relavance":"Son", "child_birth_date":"27/12/1393", "child_birth_place":"Al-Madinah Al-Munawwarah"},
            {"child_name":"Noor", "child_id":"**********", "child_relavance":"Daughter", "child_birth_date":"11/11/1400", "child_birth_place":"Al-Madinah Al-Munawwarah"},
            {"child_name":"Shahad", "child_id":"**********", "child_relavance":"Daughter", "child_birth_date":"15/02/1412", "child_birth_place":"Riyadh"},
            etc.. (Note: Do not add a comma after the last child object)
            ]
        """

    if card_type == "one-page" or card_type == "one-page-new":
        result = translate_file(family_card_file_name, card_type, required_info, optional_prompt)

        if "error" in result:
            result['result_message'] = result['error']
            return result

        # original_lines = result['original_lines']
        translated_data = result['translated_data']

        spouse_hijri_date = translated_data['spouse_birth_date']
        _, converted_gregorian_date = convert_date(spouse_hijri_date, 'h2g')
        translated_data['spouse_birth_date'] = f"{spouse_hijri_date} H\n{converted_gregorian_date} G"

        # print(f"H->G: Date object: {date_obj}, Formatted: {translated_data['spouse_birth_date']}")

        converted_children_list = convert_children_dates(translated_data['children_list'])
        # print(converted_children_list)
        translated_data['children_list'] = converted_children_list

        if card_type == "one-page":
            word_ready_dict ={
                        "family_head_name":[translated_data['family_head_name'], 0, 0, 0],
                        "family_head_id":["No: " + translated_data['family_head_id'], 0, 1, 0],
                        "card_version": ["Version " + translated_data['card_version'], 0, 1, 1],
                        "spouse_name":[translated_data['spouse_name'], 1, 1, 1],
                        "spouse_relavance":[translated_data['spouse_relavance'], 1, 1, 2],
                        "spouse_id":[translated_data['spouse_id'], 1, 1, 3],
                        "spouse_birth_place":[translated_data['spouse_birth_place'], 1, 1, 4],
                        "spouse_birth_date":[translated_data['spouse_birth_date'], 1, 1, 5],
                        "children_list":[translated_data['children_list'], 1, 2, 1],
                    }
            template_path = TEMPLATES_FOLDER + r'\Family Card Template one-page.docx'

        elif card_type == "one-page-new":
            word_ready_dict ={
                        "card_version": ["Version " + translated_data['card_version'], 0, 1, 1],
                        "family_head_name":[translated_data['family_head_name'], 1, 0, 0],
                        "family_head_id":["No: " + translated_data['family_head_id'], 1, 1, 1],
                        "date_of_issue":[translated_data['date_of_issue'], 1, 1, 3],
                        "issue_authority":[translated_data['issue_authority'], 1, 2, 1],
                        "family_members_count":[translated_data['family_members_count'], 1, 2, 3],
                        "spouse_name":[translated_data['spouse_name'], 2, 1, 1],
                        "spouse_id":[translated_data['spouse_id'], 2, 1, 2],
                        "spouse_relavance":[translated_data['spouse_relavance'], 2, 1, 3],
                        "spouse_birth_date":[translated_data['spouse_birth_date'], 2, 1, 4],
                        "spouse_birth_place":[translated_data['spouse_birth_place'], 2, 1, 5],
                        "children_list":[translated_data['children_list'], 2, 2, 1],
                    }
            template_path = TEMPLATES_FOLDER + r'\New Family Card Template (1 Page).docx'

        # if there isn't a spouse in the card, shift the start row of the children list by 1 (to cover the spouse row)
        if translated_data["spouse_relavance"] == "no spouse":
            word_ready_dict["children_list"][2] = 1

        output_path = os.path.join(DESKTOP_PATH, f"{translated_data['family_head_shortened_name']} Family Card.docx")
        fill_word_template(word_ready_dict, template_path, output_path)

    else:
        family_head_shortened_name = ""
        for page_number in range (len(family_card_file_name)):
            if page_number == 0:
                if card_type == "two-pages":
                    manual_card_type = "one-page"
                else:
                    manual_card_type = "one-page-new"
            else:
                if card_type == "two-pages":
                    manual_card_type = "two-pages"
                else:
                    manual_card_type = "two-pages-new"

            # print("card type: " + card_type)
            # print("manual_card_type: " + manual_card_type)
            result = translate_file(family_card_file_name[page_number], manual_card_type, required_info, optional_prompt)

            if "error" in result:
                result['result_message'] = result['error']
                return result

            translated_data = result['translated_data']


            spouse_hijri_date = translated_data['spouse_birth_date']
            _, converted_gregorian_date = convert_date(spouse_hijri_date, 'h2g')
            translated_data['spouse_birth_date'] = f"{spouse_hijri_date} H\n{converted_gregorian_date} G"

            # print(f"H->G: Date object: {date_obj}, Formatted: {translated_data['spouse_birth_date']}")

            converted_children_list = convert_children_dates(translated_data['children_list'])
            # print(converted_children_list)
            translated_data['children_list'] = converted_children_list


            if page_number == 0:
                if card_type == "two-pages":
                    word_ready_dict ={
                        "family_head_name":[translated_data['family_head_name'], 0, 0, 0],
                        "family_head_id":["No: " + translated_data['family_head_id'], 0, 1, 0],
                        "card_version": ["Version " + translated_data['card_version'], 0, 1, 1],
                        "spouse_name":[translated_data['spouse_name'], 1, 1, 1],
                        "spouse_relavance":["spouse", 1, 1, 2],
                        "spouse_id":[translated_data['spouse_id'], 1, 1, 3],
                        "spouse_birth_place":[translated_data['spouse_birth_place'], 1, 1, 4],
                        "spouse_birth_date":[translated_data['spouse_birth_date'], 1, 1, 5],
                        "children_list":[translated_data['children_list'], 1, 2, 1],
                    }
                    template_path = TEMPLATES_FOLDER + r'\Family Card Template two-pages.docx'

                elif card_type == "two-pages-new":
                    word_ready_dict ={
                        "card_version": ["Issue Number " + translated_data['card_version'], 0, 1, 0],
                        "family_head_name":[translated_data['family_head_name'], 1, 0, 0],
                        "family_head_id":[translated_data['family_head_id'], 1, 1, 1],
                        "date_of_issue":[translated_data['date_of_issue'], 1, 1, 3],
                        "issue_authority":[translated_data['issue_authority'], 1, 2, 1],
                        "family_members_count":[translated_data['family_members_count'], 1, 2, 3],
                        "spouse_name":[translated_data['spouse_name'], 2, 1, 1],
                        "spouse_id":[translated_data['spouse_id'], 2, 1, 2],
                        "spouse_relavance":[translated_data['spouse_relavance'], 2, 1, 3],
                        "spouse_birth_date":[translated_data['spouse_birth_date'], 2, 1, 4],
                        "spouse_birth_place":[translated_data['spouse_birth_place'], 2, 1, 5],
                        "children_list":[translated_data['children_list'], 2, 2, 1],
                    }
                    template_path = TEMPLATES_FOLDER + r'\New Family Card Template (2 Pages).docx'
                    family_head_shortened_name = translated_data['family_head_shortened_name']

                # if there isn't a spouse in the card, shift the start row of the children list by 1 (to cover the spouse row)
                if translated_data["spouse_relavance"] == "no spouse":
                    word_ready_dict["children_list"][2] = 1

                page_1_output_path = os.path.join(TEMP_DIR, 'Page1_tmp.docx')
                fill_word_template(word_ready_dict, template_path, page_1_output_path)

            else:
                if card_type == "two-pages":
                    word_ready_dict ={
                        "family_head_name":[translated_data['family_head_name'], 2, 0, 0],
                        "family_head_id":["No: " + translated_data['family_head_id'], 2, 1, 0],
                        "card_version": ["Version " + translated_data['card_version'], 2, 1, 1],
                        "issue_authority":["Issuer: " + translated_data['issue_authority'], 2, 2, 1],
                        "date_of_issue":["Issue Date: " + translated_data['date_of_issue'], 2, 2, 2],
                        "children_list":[translated_data['children_list'], 3, 1, 1],
                    }
                    family_head_shortened_name = translated_data['family_head_shortened_name']

                elif card_type == "two-pages-new":
                    word_ready_dict ={
                        "card_version": ["Issue Number " + translated_data['card_version'], 3, 1, 0],
                        "children_list":[translated_data['children_list'], 4, 1, 1],
                    }

                template_path = page_1_output_path
                output_path = os.path.join(DESKTOP_PATH, f"{family_head_shortened_name} Family Card.docx")
                fill_word_template(word_ready_dict, template_path, output_path)

    result['word_ready_dict'] = word_ready_dict
    result['result_message'] = "Family Card Translation was completed successfully, translated file was saved to your desktop"
    return result, output_path

def handle_id_translation(id_file_name, id_version="normal", optional_prompt=""):

    if id_version == "old":
        required_info = """
            "card_version": "3"
            "name":"ALANAZI, ABDULMAJEED ALI M",
            "id":"**********",
            "h_doe": "29/04/1452 H",
            "h_dob": "07/09/1419 H",
            "issuing_authority": "Riyadh",
            "birth_city": "Riyadh",
            "save_number": "23760",
            "save_authority": "Riyadh",
            "save_date": "12/12/1424 H"
        """
    else:
        required_info = """
            "id":"**********",
            "name":"ALANAZI, ABDULMAJEED ALI M",
            "g_dob": "26/12/1998 G",
            "h_dob": "07/09/1419 H",
            "g_doe": "29/08/2030 G",
            "h_doe": "29/04/1452 H",
            "birth_city": "Riyadh",
            "card_version": "3"
        """

    result = translate_file(id_file_name, "id", required_info, optional_prompt)

    if "error" in result:
        result['result_message'] = result['error']
        return result

    original_lines = result['original_lines']
    translated_data = result['translated_data']

    # Extract person's photo from the ID card using face detection
    person_image_path = crop_personal_photo_from_card(id_file_name, debugging=True)

    # If face detection fails, use a fallback or handle the error
    if person_image_path is None:
        print("Warning: Could not extract person's photo from ID card. Using placeholder.")
        # You could either skip the image or use a placeholder
        person_image_path = None  # This will be handled by the Word template function

    if id_version != "old":
        translated_data['name'] = find_keyword_line(original_lines, ",")

    template_path = TEMPLATES_FOLDER + r'\ID Template.docx'

    if id_version == "old":

        birth_hijri_date = translated_data['h_dob']
        birth_hijri_date = re.sub(r'[a-zA-Z]', '', birth_hijri_date)
        birth_hijri_date = birth_hijri_date.strip()
        _, converted_gregorian_date = convert_date(birth_hijri_date, 'h2g')
        translated_data['h_dob'] = f"{birth_hijri_date} H\n{converted_gregorian_date} G"

        expiry_hijri_date = translated_data['h_doe']
        expiry_hijri_date = re.sub(r'[a-zA-Z]', '', expiry_hijri_date)
        expiry_hijri_date = expiry_hijri_date.strip()
        _, converted_gregorian_date = convert_date(expiry_hijri_date, 'h2g')
        translated_data['h_doe'] = f"{expiry_hijri_date} H\n{converted_gregorian_date} G"

        word_ready_dict ={
            "card_version": ["Issue Number " + translated_data['card_version'], 0, 1, 0],
            "name":[translated_data['name'], 1, 0, 0],
            "id":[translated_data['id'], 1, 1, 1],
            "doe":[f"{translated_data['h_doe']}", 1, 2, 1],
            "issuing_authority":[f"{translated_data['issuing_authority']}", 1, 3, 1],
            "birth_city":[translated_data['birth_city'], 1, 4, 1],
            "dob":[f"{translated_data['h_dob']}", 1, 5, 1],
            "save_number":[f"{translated_data['save_number']}", 1, 7, 0],
            "save_authority":[f"{translated_data['save_authority']}", 1, 7, 1],
            "save_date":[f"{translated_data['save_date']}", 1, 7, 2],
            "person_image_path": [person_image_path, 2, 0, 0],
            "second_id_placement":[translated_data['id'], 3, 0, 1],
            "origin_document":[id_file_name, 4, 0, 0],
        }
    else:
        word_ready_dict ={
            "card_version": ["Issue Number " + translated_data['card_version'], 0, 1, 0],
            "name":[translated_data['name'], 1, 0, 0],
            "id":[translated_data['id'], 1, 1, 1],
            "dob":[f"{translated_data['g_dob']}\n{translated_data['h_dob']}", 1, 2, 1],
            "doe":[f"{translated_data['g_doe']}\n{translated_data['h_doe']}", 1, 3, 1],
            "birth_city":[translated_data['birth_city'], 1, 4, 1],
            "second_id_placement":[translated_data['id'], 3, 0, 1],
            "origin_document":[id_file_name, 4, 0, 0],
        }

        # Only include person image if face detection was successful
        if person_image_path is not None:
            word_ready_dict["person_image_path"] = [person_image_path, 2, 0, 0, 1.2, 1.6]  # Smaller size for ID photo: 1.2x1.6 inches

    output_path = os.path.join(DESKTOP_PATH, f"{translated_data['name']}.docx")

    if id_version == "new":
        word_ready_dict.pop("second_id_placement")
        template_path = TEMPLATES_FOLDER + r'\New ID Template.docx'

    elif id_version =="old":
        template_path = TEMPLATES_FOLDER + r'\Old ID Template.docx'


    fill_word_template(word_ready_dict, template_path, output_path)

    result['word_ready_dict'] = word_ready_dict
    result['result_message'] = "ID Translation was completed successfully, translated file was saved to your desktop"
    return result, output_path

def handle_birth_certificate_translation(birth_certificate_file_name, optional_prompt=""):

    required_info = """
                "certificate_id": "2162 - 00007242",
                "issue_number": "01",
                "issue_place": "Riyadh Civil Affairs",
                "certificate_date": "02/10/1444",
                "birth_name": "MOHAMED",
                "birth_date_h": "02/10/1444",
                "birth_date_g": "11/02/2013",
                "birth_day": "Saturday",
                "birth_gender": "Female",
                "birth_id": "1165788843",
                "birth_time": "12:51",
                "birth_place": "Riyadh",
                "family_head_name": "ALDOSARI, MOHAMED AHMED K",
                "family_head_id": "1007978123",
                "family_head_birth_place": "Riyadh",
                "family_head_birth_date": "11/12/1396",
                "mother_name": "ALAHMADI, SARAH ABDULLAH S",
                "mother_id": "1007978124",
                "mother_birth_place": "Jeddah",
                "mother_birth_date": "11/12/1396",
                "mother_nationality": "Saudi Arabia"
    """

    result = translate_file(birth_certificate_file_name,
                            "birth_certificate", required_info, optional_prompt)

    if "error" in result:
        result['result_message'] = result['error']
        return result

    # original_lines = result['original_lines']
    translated_data = result['translated_data']

    word_ready_dict ={
        "certificate_id": [translated_data['certificate_id'], 0, 0, 1],
        "issue_number":["Number: " + translated_data['issue_number'], 0, 0, 2],
        "issue_place":[translated_data['issue_place'], 0, 0, 4],
        "certificate_date":[translated_data['certificate_date'], 0, 1, 1],
        "birth_name":[translated_data['birth_name'], 0, 3, 1],
        "birth_id":[translated_data['birth_id'], 0, 3, 4],
        "birth_date_h":[translated_data['birth_date_h'], 0, 4, 1],
        "birth_date_g":[translated_data['birth_date_g'], 0, 4, 4],
        "birth_day":[translated_data['birth_day'], 0, 5, 1],
        "birth_time":[translated_data['birth_time'], 0, 5, 4],
        "birth_gender":[translated_data['birth_gender'], 0, 6, 1],
        "birth_place":[translated_data['birth_place'], 0, 6, 4],
        "family_head_name":[translated_data['family_head_name'], 0, 8, 1],
        "family_head_id":[translated_data['family_head_id'], 0, 9, 1],
        "family_head_birth_place":[translated_data['family_head_birth_place'], 0, 10, 1],
        "family_head_birth_date":[translated_data['family_head_birth_date'], 0, 10, 4],
        "mother_name":[translated_data['mother_name'], 0, 12, 1],
        "mother_id":[translated_data['mother_id'], 0, 13, 1],
        "mother_nationality":[translated_data['mother_nationality'], 0, 13, 4],
        "mother_birth_place":[translated_data['mother_birth_place'], 0, 14, 1],
        "mother_birth_date":[translated_data['mother_birth_date'], 0, 14, 4],
    }
    template_path = TEMPLATES_FOLDER + r'\Birth Certificate Template.docx'
    output_path = os.path.join(DESKTOP_PATH, f"{translated_data['birth_name']} Birth Certificate.docx")
    fill_word_template(word_ready_dict, template_path, output_path)

    result['word_ready_dict'] = word_ready_dict
    result['result_message'] = "The Birth Certificate Translation was completed successfully, translated file was saved to your desktop"
    return result, output_path

def handle_iqama_translation(iqama_file_name, iqama_type="absher", optional_prompt=""):

    if iqama_type == "absher":

        required_info = """
            "version": "5",
            "name": "REGIE DEOQUIN GILBAS",
            "id_number": "2422568911",
            "date_of_birth": "26/12/1998",
            "nationality": "Egypt",
            "date_of_expiry": "02/07/2025",
            "place_of_birth": "Saudi Arabia",
            "religion": "Islam",
            "occupation": "Mechanical Maintenance Assistant",
            "employer_id": "7012243817",
            "place_of_issue": "Riyadh Passports Office",
            "place_of_work": "Al-Madinah Al-Munawwarah",
            "emplyer_name": "Petromin Express for Car Services - One-Person Company"
        """

    elif iqama_type == "dependent-absher":

        required_info = """
            "version": "5",
            "name": "REGIE DEOQUIN GILBAS",
            "id_number": "2422568911",
            "date_of_birth": "26/12/1998",
            "nationality": "Egypt",
            "date_of_expiry": "02/07/2025",
            "place_of_birth": "Saudi Arabia",
            "religion": "Islam",
            "relevance": "Wife",
            "place_of_issue": "Riyadh Passports Office",
            "family_head_name": "RAMADAN AMJAD BELAMRI",
            "family_head_id": "2525816282"
        """

    elif iqama_type == "normal":

        required_info = """
            "name": "REGIE DEOQUIN GILBAS",
            "id_number": "2422568911",
            "version": "5",
            "place_of_issue": "Riyadh Passports Office",
            "date_of_issue": "02/07/2020",
            "date_of_birth": "26/12/1998",
            "occupation": "Mechanical Maintenance Assistant",
            "nationality": "Pakistan",
            "religion": "Islam",
            "employer": "AL-KHALIJ Company For Trading - One Person Company(lEAVE EMPTY IF NOT AVAILABLE)"
        """

    elif iqama_type == "dependent-normal":

        required_info = """
            "name": "REGIE DEOQUIN GILBAS",
            "id_number": "2422568911",
            "version": "5",
            "place_of_issue": "Riyadh Civil Affairs",
            "date_of_expiry": "02/07/2025",
            "date_of_birth": "26/12/1998",
            "nationality": "Egypt",
            "religion": "Islam",
            "family_head_id": "2525816282"
            "family_head_name": "RAMADAN AMJAD BELAMRI",
            "relevance": "Wife"
        """

    result = translate_file(iqama_file_name, "iqama", required_info, optional_prompt)

    if "error" in result:
        result['result_message'] = result['error']
        return result

    # original_lines = result['original_lines']
    translated_data = result['translated_data']

    template_path = TEMPLATES_FOLDER + r'\Absher Iqama Template.docx'
    output_path = os.path.join(DESKTOP_PATH, f"{translated_data['name']}.docx")

    if iqama_type == "absher":

        word_ready_dict ={
        "version": ["Issue Number: " + translated_data['version'], 0, 0, 1],
        "name":[translated_data['name'], 1, 0, 0],
        "id_number":[translated_data['id_number'], 1, 1, 1],
        "date_of_birth":[translated_data['date_of_birth'], 1, 2, 1],
        "nationality":[translated_data['nationality'], 1, 3, 1],
        "date_of_expiry":[translated_data['date_of_expiry'], 1, 1, 3],
        "place_of_birth":[translated_data['place_of_birth'], 1, 2, 3],
        "religion":[translated_data['religion'], 1, 3, 3],
        "occupation":[translated_data['occupation'], 1, 4, 1],
        "employer_id":[translated_data['employer_id'], 1, 5, 1],
        "place_of_issue":[translated_data['place_of_issue'], 1, 6, 1],
        "place_of_work":[translated_data['place_of_work'], 1, 7, 1],
        "emplyer_name":[translated_data['emplyer_name'], 1, 8, 1],
    }

    elif iqama_type == "dependent-absher":

        word_ready_dict ={
        "version": ["Issue Number: " + translated_data['version'], 0, 0, 1],
        "name":[translated_data['name'], 1, 0, 0],
        "id_number":[translated_data['id_number'], 1, 1, 1],
        "date_of_birth":[translated_data['date_of_birth'], 1, 2, 1],
        "nationality":[translated_data['nationality'], 1, 3, 1],
        "date_of_expiry":[translated_data['date_of_expiry'], 1, 1, 3],
        "place_of_birth":[translated_data['place_of_birth'], 1, 2, 3],
        "religion":[translated_data['religion'], 1, 3, 3],
        "relevance":[translated_data['relevance'], 1, 4, 1],
        "place_of_issue":[translated_data['place_of_issue'], 1, 5, 1],
        "family_head_name":[translated_data['family_head_name'], 1, 6, 1],
        "family_head_id":[translated_data['family_head_id'], 1, 7, 1],
    }
        template_path = TEMPLATES_FOLDER + r'\Dependent(Absher) Iqama Template.docx'

    elif iqama_type == "normal":

        _, converted_gregorian_date = convert_date(translated_data['date_of_issue'], 'h2g')
        translated_data['date_of_issue'] = f"{translated_data['date_of_issue']} H\n{converted_gregorian_date} G"

        word_ready_dict ={
        "name":[translated_data['name'], 0, 0, 0],
        "id_number":[translated_data['id_number'], 0, 1, 1],
        "version": [translated_data['version'], 0, 1, 3],
        "place_of_issue":[translated_data['place_of_issue'], 0, 2, 1],
        "date_of_issue":[translated_data['date_of_issue'], 0, 3, 1],
        "date_of_birth":[translated_data['date_of_birth'], 0, 3, 3],
        "occupation":[translated_data['occupation'], 0, 4, 1],
        "nationality":[translated_data['nationality'], 0, 5, 1],
        "religion":[translated_data['religion'], 0, 5, 3],
        "employer":[translated_data['employer'], 0, 6, 1],
        "id_number2":[translated_data['id_number'], 1, 1, 0],
    }
        template_path = TEMPLATES_FOLDER + r'\Normal Iqama Template.docx'

    elif iqama_type == "dependent-normal":

        _, converted_gregorian_date = convert_date(translated_data['date_of_expiry'], 'h2g')
        translated_data['date_of_expiry'] = f"{translated_data['date_of_expiry']} H\n{converted_gregorian_date} G"


        word_ready_dict ={
        "name":[translated_data['name'], 0, 0, 0],
        "id_number":[translated_data['id_number'], 0, 1, 1],
        "version": [translated_data['version'], 0, 1, 3],
        "place_of_issue":[translated_data['place_of_issue'], 0, 2, 1],
        "date_of_expiry":[translated_data['date_of_expiry'], 0, 3, 1],
        "date_of_birth":[translated_data['date_of_birth'], 0, 3, 3],
        "nationality":[translated_data['nationality'], 0, 4, 1],
        "religion":[translated_data['religion'], 0, 4, 3],
        "family_head_id":[translated_data['family_head_id'], 0, 5, 1],
        "family_head_name":[translated_data['family_head_name'], 0, 6, 1],
        "relevance":[translated_data['relevance'], 0, 7, 1],
        "id_number2":[translated_data['id_number'], 1, 1, 0],

    }
        template_path = TEMPLATES_FOLDER + r'\Dependent (Normal) Iqama Template.docx'

    fill_word_template(word_ready_dict, template_path, output_path)

    result['word_ready_dict'] = word_ready_dict
    result['result_message'] = "The Iqama Translation was completed successfully, translated file was saved to your desktop"
    return result, output_path

def handle_trademark_translation(image_path, tm_type, optional_prompt=""):

    full_prompt = fetch_ai_prompt("tm")
    full_prompt = full_prompt + optional_prompt

    # meaning_prompt = fetch_ai_prompt("tm-meaning")
    # description_prompt = fetch_ai_prompt("tm-description")

    if tm_translation_provider == "openrouter":
        json_response = call_openrouter(default_openrouter_model, full_prompt, fallback_models, image_path)

    elif tm_translation_provider == "google":
        json_response = call_gemini_v2(default_tm_model_google, full_prompt, image_path)

    meaning_text = json_response['meaning']

    print(json_response)
    print("The meaning is:" + meaning_text)


    if tm_type == "full":
        word_ready_dict ={
            "tm_language": [json_response['tm_language'], 0, 0, 0],
            "ar_name": [json_response['ar_name'], 0, 1, 4],
            "en_name":[json_response['en_name'], 0, 1, 0],
            "meaning":[json_response['meaning'], 0, 2, 0],
            "description":[json_response['description'], 0, 3, 0],
            "pronunciation":[json_response['pronunciation'], 0, 4, 3]
        }

        template_path = os.path.join(TEMPLATES_FOLDER, 'TM-Full-Template.docx')

    elif tm_type == "meaning":
        word_ready_dict ={
            "en_name":[json_response['en_name'] +
                       "\n" + json_response['ar_name'], 0, 0, 0],
            "meaning":[json_response['meaning'], 0, 1, 0],
            # "description":[json_response['description'], 0, 2, 0],
            "pronunciation":[json_response['pronunciation'], 0, 2, 0]
        }
        template_path = os.path.join(TEMPLATES_FOLDER, 'TM-Meaning-Only-Template.docx')

    elif tm_type == "ashraf":
        word_ready_dict ={
            "en_name":[json_response['en_name'] +
                       "\n" + json_response['ar_name'], 1, 0, 0],
            "meaning":[json_response['meaning'], 1, 1, 0],
            "description":[json_response['description'], 1, 2, 0],
            "pronunciation":[json_response['pronunciation'], 1, 3, 0]
        }
        template_path = os.path.join(TEMPLATES_FOLDER, 'TM-Ashraf-Template.docx')

    elif tm_type == "yasser":
        word_ready_dict ={
            "en_name":[json_response['en_name'] +
                       "\n" + json_response['ar_name'], 0, 0, 0],
            "meaning":[json_response['meaning'], 0, 1, 0],
            "description":[json_response['description'], 0, 2, 0],
            "pronunciation":[json_response['pronunciation'], 0, 3, 0]
        }
        template_path = os.path.join(TEMPLATES_FOLDER, 'TM-Yasser-Template.docx')

    output_path = os.path.join(DESKTOP_PATH, f"{json_response['main_name']} - ترجمة العلامة التجارية .docx")

    fill_word_template(word_ready_dict, template_path, output_path)
    # print(output_path)


    print(json_response)

    # print("\n\n" + str(format_trademark_info(json_response)) + "\n\n")

    full_text, office_text = format_trademark_info(json_response)

    return json_response, meaning_text, output_path, full_text, office_text

def handle_auto_mode(uploaded_files):
    """
    Handle auto mode processing for multiple files.
    Processes PDFs and images, compresses them, crops cards, rotates correctly,
    and identifies document types with automatic renaming.

    Args:
        uploaded_files: List of uploaded files from Flask request

    Returns:
        tuple: (result_dict, output_path)
    """
    from datetime import datetime
    import uuid
    import time

    # 1. Create unique task folder
    current_date = datetime.now()
    date_base = current_date.strftime("%d-%m-%y")
    task_number = 1
    while True:
        date_folder_name = f"{date_base}-task_{task_number}"
        date_folder_path = os.path.join(UPLOAD_FOLDER, date_folder_name)
        if not os.path.exists(date_folder_path):
            break
        task_number += 1

    os.makedirs(date_folder_path, exist_ok=True)
    cropped_cards_path = os.path.join(date_folder_path, "cropped_cards")
    os.makedirs(cropped_cards_path, exist_ok=True)

    print(f"\n=== AUTO MODE PROCESSING ===")
    print(f"Task folder: {date_folder_name}")
    print(f"Processing {len(uploaded_files)} files")

    all_processed_images = []

    # 2. Process each uploaded file (PDFs and images)
    for uploaded_file in uploaded_files:
        if uploaded_file.filename == '':
            continue

        print(f"\n=== Processing file: {uploaded_file.filename} ===")

        # Save uploaded file temporarily
        file_extension = os.path.splitext(uploaded_file.filename)[1].lower()
        safe_filename = f"temp_{int(time.time())}_{uuid.uuid4().hex[:8]}{file_extension}"
        temp_file_path = os.path.join(app.config['UPLOAD_FOLDER'], safe_filename)

        try:
            uploaded_file.seek(0)
            uploaded_file.save(temp_file_path)

            # Verify file was saved correctly
            if not os.path.exists(temp_file_path) or os.path.getsize(temp_file_path) == 0:
                print(f"ERROR: File {uploaded_file.filename} was not saved correctly")
                continue

            print(f"✓ File {uploaded_file.filename} saved successfully")

        except Exception as e:
            print(f"ERROR saving file {uploaded_file.filename}: {str(e)}")
            continue

        # 3. Convert PDFs to images or process images directly
        if file_extension == '.pdf':
            processed_images = process_pdf_file(temp_file_path, date_folder_path, debugging=True)
            all_processed_images.extend(processed_images)

            # Clean up temp PDF file
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)

        elif file_extension in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']:
            processed_image = process_image_file(temp_file_path, date_folder_path, uploaded_file.filename, debugging=True)
            if processed_image:
                all_processed_images.append(processed_image)
        else:
            print(f"Unsupported file type: {uploaded_file.filename}")
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)

    # 4. Crop cards, rotate correctly, and classify document types
    all_classified_cards = crop_and_classify_images(all_processed_images, cropped_cards_path, debugging=True)

    # 5. Prepare results for display
    print(f"\n=== AUTO MODE SUMMARY ===")
    print(f"Total images processed: {len(all_processed_images)}")
    print(f"Total classified cards: {len(all_classified_cards)}")
    print(f"Task folder: {date_folder_name}")

    # Create detailed result information
    card_types = {}
    for card_path in all_classified_cards:
        card_name = os.path.basename(card_path)
        card_type = card_name.split('_')[0] + '_' + card_name.split('_')[1] if '_' in card_name else 'Unknown'
        card_types[card_type] = card_types.get(card_type, 0) + 1

    result_lines = []
    result_lines.append(f"📁 Task folder: {date_folder_name}")
    result_lines.append(f"📄 Files processed: {len(all_processed_images)}")
    result_lines.append(f"🎯 Cards identified and classified: {len(all_classified_cards)}")
    result_lines.append("")
    result_lines.append("📋 Document types found:")
    for card_type, count in card_types.items():
        result_lines.append(f"  • {card_type.replace('_', ' ')}: {count}")

    result = {
        'result_message': f"Auto processing completed successfully. {len(all_processed_images)} files processed, {len(all_classified_cards)} cards identified and classified. Task folder: {date_folder_name}",
        'ocr_lines': result_lines,
        'original_lines': [f"Classified card: {os.path.basename(card)}" for card in all_classified_cards],
        'ai_response': f"Files processed: {len(all_processed_images)}\nClassified cards: {len(all_classified_cards)}\nTask folder: {date_folder_name}\n\nDocument types:\n" + "\n".join([f"{k.replace('_', ' ')}: {v}" for k, v in card_types.items()]),
        'word_ready_dict': {}
    }

    return result, date_folder_path

@app.route('/download_file')
def download_file():
    # Get the encoded file path from the URL
    file_path = request.args.get('file_path')

    # Decode the file path to handle special characters
    try:
        # First try to decode in case it's already URL encoded
        file_path = urllib.parse.unquote(file_path)
    except Exception as e:
        print(f"Error decoding file path: {e}")

    # Reconstruct the proper path
    correct_path = os.path.normpath(file_path)  # This will fix the path format

    print(f"Attempting to access file at: {correct_path}")  # Debug print

    if os.path.exists(correct_path):
        # Get the filename for download
        filename = os.path.basename(correct_path)

        return send_file(
            correct_path,
            as_attachment=True,
            download_name=filename
        )
    else:
        return f"File not found at path: {correct_path}", 404

@app.route('/update_settings', methods=['POST'])
def update_settings():
    global document_translation_provider, tm_translation_provider, default_openrouter_model

    try:
        data = request.get_json()

        if 'document_translation_provider' in data:
            document_translation_provider = data['document_translation_provider']

        if 'tm_translation_provider' in data:
            tm_translation_provider = data['tm_translation_provider']

        if 'openrouter_model' in data:
            default_openrouter_model = data['openrouter_model']

        # Get updated settings dictionary
        settings = get_model_settings()

        return jsonify({
            'success': True,
            'message': 'Settings updated successfully',
            'settings': settings
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error updating settings: {str(e)}'
        }), 400

@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        # Debug print all form data
        print("\n=== Form Data ===")
        for key, value in request.form.items():
            print(f"{key}: {value}")

        print("\n=== Files ===")
        for key, file in request.files.items():
            print(f"{key}: {file.filename}")

        if len(request.form.get('optional_prompt')):
            optional_prompt = request.form.get('optional_prompt')

            optional_prompt = f"""\n\n
            Task Notes / Correct Spelling of names (use them exactly as they appear below):\n
            {optional_prompt}
            """
            print ("the prompt is: "+ optional_prompt)
        else:
            optional_prompt = ""

        if 'file' not in request.files:
            model_settings = get_model_settings()
            return render_template('index.html', error="No file part", **model_settings)

        file = request.files['file']
        if file.filename == '':
            model_settings = get_model_settings()
            return render_template('index.html', error="No selected file", **model_settings)

        if file:
            filename = os.path.join(app.config['UPLOAD_FOLDER'], file.filename)
            file.save(filename)

            # Handle image flipping
            flip_direction = request.form.get('flip')
            if flip_direction:
                filename = flip_image(filename, flip_direction)

            document_type = request.form.get('document-type')
            if not document_type:
                return redirect(url_for('index'))

            if document_type == "certification":
                # if 'file' not in request.files:
                #     return render_template('index.html', error="No files uploaded")

                try:
                    translated_path, source_path = process_certification_files(request.files.getlist('file'))
                    result = certify_document(translated_path, source_path)
                except ValueError as e:
                    model_settings = get_model_settings()
                    return render_template('index.html', error=str(e), **model_settings)
                model_settings = get_model_settings()
                return render_template('index.html', **model_settings)

            elif document_type == "file":

                ai_response = translate_custom_file(filename, optional_prompt)
                model_settings = get_model_settings()
                return render_template('index.html', ocr_lines=ai_response, ai_response=ai_response, **model_settings)

            elif document_type == "id":
                card_version = request.form.get('id-type')
                result, output_path = handle_id_translation(filename, card_version, optional_prompt)

            elif document_type == "birth-certificate":
                result, output_path = handle_birth_certificate_translation(filename, optional_prompt)

            elif document_type == "iqama":
                iqama_type = request.form.get('iqama-type')
                result, output_path = handle_iqama_translation(filename, iqama_type, optional_prompt)

            elif document_type == "tm":
                tm_type = request.form.get('tm-type')
                result, meaning_text, output_path, full_text, office_text = handle_trademark_translation(filename, tm_type, optional_prompt)
                # print(output_path)
                print(f"Debug - output_path: {output_path}")  # Add this line
                model_settings = get_model_settings()
                return render_template('index.html', message="Trademark Translation Compelted Successfully", ai_response=result,
                                       ocr_lines=result, meaning_text=meaning_text, output_path=output_path,
                                       full_text=full_text, office_text=office_text, **model_settings)

            elif document_type == "family-card":
                family_card_type = request.form.get('family-card-type')

                if family_card_type == "two-pages" or family_card_type == "two-pages-new":
                    second_page = request.files['second-file']

                    if second_page.filename == '':
                        model_settings = get_model_settings()
                        return render_template('index.html', error="No selected file", **model_settings)

                    if second_page:
                        second_page_filename = os.path.join(app.config['UPLOAD_FOLDER'], second_page.filename)
                        second_page.save(second_page_filename)

                        # Handle flipping for the second page
                        if flip_direction:
                            second_page_filename = flip_image(second_page_filename, flip_direction)

                        files_names_list = [filename, second_page_filename]
                        result, output_path = handle_family_card_translation(files_names_list, family_card_type, optional_prompt)

                else:
                    result, output_path = handle_family_card_translation(filename, family_card_type, optional_prompt)

            elif document_type == "auto":
                # Get all uploaded files
                uploaded_files = request.files.getlist('file')

                if not uploaded_files or len(uploaded_files) == 0:
                    model_settings = get_model_settings()
                    return render_template('index.html', error="No files uploaded for auto processing", **model_settings)

                # Process files using the dedicated auto mode handler
                result, output_path = handle_auto_mode(uploaded_files)

            model_settings = get_model_settings()
            return render_template('index.html', message=result['result_message'], ocr_lines=result['ocr_lines'], original_lines=result['original_lines'],
                                    ai_response=result['ai_response'], ready_data=result['word_ready_dict'], output_path=output_path, **model_settings)

    # Get model settings dictionary
    model_settings = get_model_settings()
    return render_template('index.html', **model_settings)


if __name__ == "__main__":
    app.run(host='0.0.0.0', port=5000, debug=True)
