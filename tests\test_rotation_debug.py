#!/usr/bin/env python3
"""
Test script to debug the double rotation issue with test9.jpg and test9 - Copy.jpg
"""

import os
import cv2
import shutil
from helpers import crop_cards

def test_rotation_issue():
    """Test the rotation issue with the problematic files"""
    
    test_files = [
        "test_files/test9.jpg",
        "test_files/test9 - Copy.jpg"
    ]
    
    for test_file in test_files:
        if not os.path.exists(test_file):
            print(f"❌ Test file not found: {test_file}")
            continue
            
        print(f"\n{'='*60}")
        print(f"Testing rotation issue with: {test_file}")
        print(f"{'='*60}")
        
        # Create a backup of the original file
        backup_file = test_file.replace(".jpg", "_backup.jpg")
        shutil.copy2(test_file, backup_file)
        
        try:
            # Run the card detection which includes rotation detection
            result = crop_cards(test_file, cards_to_detect=2, debugging=True)
            
            print(f"\n📊 Results for {test_file}:")
            print(f"   Cards detected: {len(result) if result else 0}")
            
            # Check if the image was modified
            original_image = cv2.imread(backup_file)
            current_image = cv2.imread(test_file)
            
            if original_image is not None and current_image is not None:
                # Compare dimensions to see if rotation occurred
                orig_h, orig_w = original_image.shape[:2]
                curr_h, curr_w = current_image.shape[:2]
                
                print(f"   Original dimensions: {orig_w}x{orig_h}")
                print(f"   Current dimensions: {curr_w}x{curr_h}")
                
                if (orig_w, orig_h) != (curr_w, curr_h):
                    print(f"   ⚠️  Image dimensions changed - rotation was applied")
                else:
                    print(f"   ✓ Image dimensions unchanged")
            
        except Exception as e:
            print(f"❌ Error testing {test_file}: {e}")
        
        finally:
            # Restore the original file
            if os.path.exists(backup_file):
                shutil.copy2(backup_file, test_file)
                os.remove(backup_file)

if __name__ == "__main__":
    test_rotation_issue()
