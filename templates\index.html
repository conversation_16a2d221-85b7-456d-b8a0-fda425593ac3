<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Swift Switch - Document translation and processing tool">
    <meta name="author" content="Swift Switch Team">
    <title>Swift Switch</title>

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- External Libraries -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/jquery-jsonview/1.2.3/jquery.jsonview.min.css" rel="stylesheet">

    <!-- Application Styles and Favicon -->
    <link href="{{ url_for('static', filename='index.css') }}" rel="stylesheet">
    <link rel="shortcut icon" href="{{ url_for('static', filename='swift switch favicon.png') }}">
</head>
<body>
    <!-- Notification container for displaying messages -->
    <div class="notification-container" id="notification-container"></div>

    <!-- Main container for the application layout -->
    <div class="container">
        <!-- Sidebar Section - Contains additional options and controls -->
        <aside class="sidebar" role="complementary">
            <div class="sidebar-header">
                <h2>Options</h2>
            </div>
            <!-- Additional prompt section for custom processing instructions -->
            <div class="prompt-section">
                <h3>Additional Prompt</h3>
                <textarea id="optional_prompt" class="prompt-input" placeholder="Enter your prompt here..." aria-label="Additional prompt for processing"></textarea>
            </div>

            <!-- Translation Provider Settings -->
            <div class="translation-settings">
                <h3>Model Settings</h3>
                <div class="settings-form">
                    <div class="form-group">
                        <label for="document_translation_provider">Document Translation Provider</label>
                        <select id="document_translation_provider" class="settings-select">
                            <option value="google" {% if document_translation_provider == 'google' %}selected{% endif %}>Google</option>
                            <option value="openrouter" {% if document_translation_provider == 'openrouter' %}selected{% endif %}>OpenRouter</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="tm_translation_provider">TM Translation Provider</label>
                        <select id="tm_translation_provider" class="settings-select">
                            <option value="google" {% if tm_translation_provider == 'google' %}selected{% endif %}>Google</option>
                            <option value="openrouter" {% if tm_translation_provider == 'openrouter' %}selected{% endif %}>OpenRouter</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="openrouter_model">OpenRouter Model</label>
                        <input type="text" id="openrouter_model" class="settings-input" value="{{ default_openrouter_model }}">
                    </div>

                    <button type="button" id="save_settings" class="settings-save-btn">Save Settings</button>
                </div>
            </div>
        </aside>

        <!-- Main Content Section - Contains the primary application interface -->
        <main class="main_container" role="main">
            <h1 class="app-title">Swift Switch</h1>

            <!-- File Upload Section - For document upload and processing -->
            <div class="file-upload-container">
                <!-- Main form for document upload and processing -->
                <form id="upload-form" method="post" enctype="multipart/form-data" novalidate>
                    <!-- Image Rotation Options - Controls for rotating uploaded images -->
                    <div class="flip-select" role="radiogroup" aria-labelledby="rotation-options">
                        <span id="rotation-options" class="sr-only">Image Rotation Options</span>
                        <input type="radio" id="flip-left" name="flip" value="-90">
                        <label for="flip-left">Flip -90°</label>
                        <input type="radio" id="no-flip" name="flip" value="0">
                        <label for="no-flip">Cancel</label>
                        <input type="radio" id="flip-right" name="flip" value="90">
                        <label for="flip-right">Flip +90°</label>
                    </div>

                    <!-- Download button section - Only appears when a processed document is available -->
                    {% if output_path %}
                    <button type="button" onclick="downloadFile('{{ output_path|replace('\\', '\\\\') }}')" class="download-button">
                        Download Document
                    </button>
                    {% endif %}

                    <div class="two-column-layout">
                        <!-- Left Column - Contains file drop area -->
                        <div class="left-column">
                            <!-- Main File Drop Area - Primary interface for file uploads -->
                            <div id="drop-area" class="drop-area" tabindex="0" aria-label="File upload area">
                                <p>Drag and drop file here or click to select</p>
                                <input type="file" name="file" id="fileElem" accept=".docx,.pdf,.jpg,.jpeg,.png" multiple hidden>
                                <!-- Preview area for uploaded files -->
                                <div id="preview-area" class="preview-area" aria-live="polite"></div>
                            </div>
                            <!-- Display area for the names of selected files -->
                            <div id="file-name" class="file-name" aria-live="polite"></div>
                            <!-- Clear button for uploaded files -->
                            <button type="button" id="clear-files-btn" class="clear-files-btn" style="display: none;">Clear All Images</button>

                            <!-- Second Page Upload - Only shown for multi-page documents like family cards -->
                            <div id="second-page-upload" style="display: none;" aria-hidden="true">
                                <div id="second-drop-area" class="drop-area" tabindex="0" aria-label="Second page upload area">
                                    <p>Drag and drop second page here or click to select</p>
                                    <input type="file" name="second-file" id="secondFileElem" accept="image/*" hidden>
                                </div>
                                <!-- Display area for the name of the selected second page file -->
                                <div id="second-file-name" class="file-name" aria-live="polite"></div>
                            </div>
                        </div>

                        <!-- Right Column - Contains document type selection -->
                        <div class="right-column">
                            <!-- Document Type Selection - Primary categorization of the document to be processed -->
                            <div class="radio-group" id="document-type-group" role="radiogroup" aria-labelledby="document-type-heading">
                                <h3 id="document-type-heading">Select Document Type</h3>
                                <div class="radio-options">
                                    <div class="radio-option">
                                        <input type="radio" id="auto" name="document-type" value="auto">
                                        <label for="auto">Auto</label>
                                    </div>
                                    <div class="radio-option">
                                        <input type="radio" id="id" name="document-type" value="id">
                                        <label for="id">ID</label>
                                    </div>
                                    <div class="radio-option">
                                        <input type="radio" id="iqama" name="document-type" value="iqama">
                                        <label for="iqama">Iqama</label>
                                    </div>
                                    <div class="radio-option">
                                        <input type="radio" id="family-card" name="document-type" value="family-card">
                                        <label for="family-card">Family Card</label>
                                    </div>
                                    <div class="radio-option">
                                        <input type="radio" id="tm" name="document-type" value="tm">
                                        <label for="tm">Trademark</label>
                                    </div>
                                    <div class="radio-option">
                                        <input type="radio" id="birth-certificate" name="document-type" value="birth-certificate">
                                        <label for="birth-certificate">Birth Certificate</label>
                                    </div>
                                    <div class="radio-option">
                                        <input type="radio" id="file" name="document-type" value="file">
                                        <label for="file">File</label>
                                    </div>
                                </div>
                            </div>

                            <!-- Sub-options for document types -->
                            <div id="id-types" class="radio-group sub-options" style="display: none;">
                                <h3>Select ID Type:</h3>
                                <div class="radio-options">
                                    <div class="radio-option">
                                        <input type="radio" id="new-id" name="id-type" value="new">
                                        <label for="new-id">New</label>
                                    </div>
                                    <div class="radio-option">
                                        <input type="radio" id="normal-id" name="id-type" value="normal" checked>
                                        <label for="normal-id">Normal</label>
                                    </div>
                                    <div class="radio-option">
                                        <input type="radio" id="old-id" name="id-type" value="old">
                                        <label for="old-id">Old</label>
                                    </div>
                                </div>
                            </div>

                            <div id="tm-types" class="radio-group sub-options" style="display: none;">
                                <h3>Select Trademark Template:</h3>
                                <div class="radio-options">
                                    <div class="radio-option">
                                        <input type="radio" id="full_tm" name="tm-type" value="full" checked>
                                        <label for="full_tm">Full</label>
                                    </div>
                                    <div class="radio-option">
                                        <input type="radio" id="meaning_tm" name="tm-type" value="meaning">
                                        <label for="meaning_tm">Meaning</label>
                                    </div>
                                    <div class="radio-option">
                                        <input type="radio" id="ashraf_tm" name="tm-type" value="ashraf">
                                        <label for="ashraf_tm">Ashraf</label>
                                    </div>
                                    <div class="radio-option">
                                        <input type="radio" id="yasser_tm" name="tm-type" value="yasser">
                                        <label for="yasser_tm">Yasser</label>
                                    </div>
                                </div>
                            </div>

                            <div id="iqama-types" class="radio-group sub-options" style="display: none;">
                                <h3>Select Iqama Type:</h3>
                                <div class="radio-options">
                                    <div class="radio-option">
                                        <input type="radio" id="absher-iqama" name="iqama-type" value="absher" checked>
                                        <label for="absher-iqama">Absher</label>
                                    </div>
                                    <div class="radio-option">
                                        <input type="radio" id="normal-iqama" name="iqama-type" value="normal">
                                        <label for="normal-iqama">Normal</label>
                                    </div>
                                    <div class="radio-option">
                                        <input type="radio" id="dependent-absher-iqama" name="iqama-type" value="dependent-absher">
                                        <label for="dependent-absher-iqama">Dependent (Absher)</label>
                                    </div>
                                    <div class="radio-option">
                                        <input type="radio" id="dependent-normal-iqama" name="iqama-type" value="dependent-normal">
                                        <label for="dependent-normal-iqama">Dependent (Normal)</label>
                                    </div>
                                </div>
                            </div>

                            <div id="family-card-types" class="radio-group sub-options" style="display: none;">
                                <h3>Select Family Card Type:</h3>
                                <div class="radio-options">
                                    <div class="radio-option">
                                        <input type="radio" id="one-page" name="family-card-type" value="one-page" checked>
                                        <label for="one-page">One Page</label>
                                    </div>
                                    <div class="radio-option">
                                        <input type="radio" id="two-pages" name="family-card-type" value="two-pages">
                                        <label for="two-pages">Two Pages</label>
                                    </div>
                                    <div class="radio-option">
                                        <input type="radio" id="one-page-new" name="family-card-type" value="one-page-new">
                                        <label for="one-page-new">One Page (New)</label>
                                    </div>
                                    <div class="radio-option">
                                        <input type="radio" id="two-pages-new" name="family-card-type" value="two-pages-new">
                                        <label for="two-pages-new">Two Pages (New)</label>
                                    </div>
                                </div>
                            </div>
                        </div> <!-- Close right-column -->
                    </div> <!-- Close two-column-layout -->

                    <!-- Error Message Display - Shows validation errors and other issues -->
                    <div id="error-message" class="error-message" role="alert" aria-live="assertive"></div>

                    <!-- Submit Button - Initiates the document processing -->
                    <button type="submit" id="submit-button">Translate!</button>
                </form>
            </div>

            <!-- Status Messages Section - Displays success and error messages -->
            {% if message or error or get_flashed_messages(with_categories=true) %}
            <div id="status-messages" class="sr-only" aria-hidden="true">
                {% if message %}
                <p class="status-message success-message" role="status">{{ message }}</p>
                {% endif %}

                {% if error %}
                <p class="status-message error-notification" role="alert">{{ error }}</p>
                {% endif %}

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                        <div class="alert alert-{{ category }}" role="alert">{{ message }}</div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
            </div>
            {% endif %}

            <!-- Results Sections - Only displayed when data is available -->
            {% if ready_data or ai_response or meaning_text or ocr_lines or original_lines %}
            <div class="results-container">
                <!-- Processed Data Results Section -->
                {% if ready_data %}
                <section class="results-section">
                    <h2>Ready Data:</h2>
                    <div class="data-list">
                        {% for key, value in ready_data.items() %}
                        <div class="data-item">
                            <span class="data-key">{{ key }}:</span>
                            <span class="data-value">{{ value }}</span>
                        </div>
                        {% endfor %}
                    </div>
                </section>
                {% endif %}

                <!-- AI Response Section -->
                {% if ai_response %}
                <section class="results-section">
                    <h2>AI Response:</h2>
                    <pre class="ai-response">{{ ai_response }}</pre>
                </section>
                {% endif %}

                <!-- Ready to copy Section -->
                {% if full_text %}
                <section class="results-section">
                    <div class="section-header">
                        <h2>TM Full Text</h2>
                        <button type="button" class="copy-btn" data-target="full-text-content" aria-label="Copy full text content">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                    </div>
                    <pre id="full-text-content" class="ai-response">{{ full_text }}</pre>
                </section>
                {% endif %}

                <!-- Ready to copy Section -->
                {% if office_text %}
                <section class="results-section">
                    <div class="section-header">
                        <h2>Office Text</h2>
                        <button type="button" class="copy-btn" data-target="office-text-content" aria-label="Copy office text content">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                    </div>
                    <pre id="office-text-content" class="ai-response">{{ office_text }}</pre>
                </section>
                {% endif %}

                <!-- Meaning Text Section -->
                {% if meaning_text %}
                <section class="results-section">
                    <h2>Meaning Text:</h2>
                    <pre class="meaning-text">{{ meaning_text }}</pre>
                </section>
                {% endif %}

                <!-- OCR Results Section -->
                {% if ocr_lines %}
                <section class="results-section">
                    <h2>OCR Result:</h2>
                    <div id="json-renderer" class="json-view-container"></div>
                </section>
                {% endif %}

                <!-- Original Lines Section -->
                {% if original_lines %}
                <section class="results-section">
                    <h2>Original Lines:</h2>
                    <div class="original-lines">
                        {% for value in original_lines %}
                        <div class="line-item">{{ value }}</div>
                        {% endfor %}
                    </div>
                </section>
                {% endif %}
            </div>
            {% endif %}
        </main>
    </div>

    <!-- Scripts - Loaded at the end for better performance -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-jsonview/1.2.3/jquery.jsonview.min.js"></script>
    <script src="{{ url_for('static', filename='index.js') }}"></script>

    <!-- Notification initialization scripts -->
    {% if message %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            showNotification('{{ message }}', 'success', 'Success');
        });
    </script>
    {% endif %}

    {% if error %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            showNotification('{{ error }}', 'error', 'Error');
        });
    </script>
    {% endif %}

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    {% for category, message in messages %}
                    showNotification('{{ message }}', '{{ category }}', '{{ category|title }}');
                    {% endfor %}
                });
            </script>
        {% endif %}
    {% endwith %}

    <!-- OCR data initialization -->
    {% if ocr_lines %}
    <script>
        $(function() {
            var data = {{ ocr_lines | safe }};
            $("#json-renderer").JSONView(data, {collapsed: true});
        });
    </script>
    {% endif %}

    <!-- Download function -->
    {% if output_path %}
    <script>
        function downloadFile(path) {
            // Encode the path to handle special characters like &, ?, etc.
            const encodedPath = encodeURIComponent(path);
            window.location.href = `/download_file?file_path=${encodedPath}`;
        }
    </script>
    {% endif %}
</body>
</html>
