#!/usr/bin/env python3
"""
Test script for ID translation with face cropping integration.

This test verifies that the handle_id_translation function correctly
uses the face cropping function instead of hardcoded image paths.
"""

import os
import sys
import tempfile
from PIL import Image, ImageDraw

# Add the parent directory to the path to import app functions
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from helpers import crop_personal_photo_from_card

def create_mock_id_card(width=800, height=600, filename="mock_id_card.jpg"):
    """Create a mock ID card image with a face for testing."""
    # Create a simple ID card background
    img = Image.new('RGB', (width, height), color='lightgray')
    draw = ImageDraw.Draw(img)
    
    # Draw ID card border
    draw.rectangle([20, 20, width-20, height-20], outline='black', width=3)
    
    # Draw a simple face in the typical ID photo location (upper right)
    face_x = width - 200
    face_y = 80
    face_size = 120
    
    # Face (circle)
    draw.ellipse([face_x, face_y, face_x + face_size, face_y + face_size], 
                fill='peachpuff', outline='black', width=2)
    
    # Eyes
    eye_y = face_y + 30
    draw.ellipse([face_x + 25, eye_y, face_x + 35, eye_y + 10], fill='black')
    draw.ellipse([face_x + 85, eye_y, face_x + 95, eye_y + 10], fill='black')
    
    # Nose
    nose_y = face_y + 50
    draw.line([face_x + 60, nose_y, face_x + 60, nose_y + 15], fill='black', width=2)
    
    # Mouth
    mouth_y = face_y + 75
    draw.arc([face_x + 45, mouth_y, face_x + 75, mouth_y + 15], 0, 180, fill='black', width=2)
    
    # Add some text to make it look like an ID
    draw.text((50, 100), "KINGDOM OF SAUDI ARABIA", fill='black')
    draw.text((50, 130), "NATIONAL ID CARD", fill='black')
    draw.text((50, 200), "Name: JOHN DOE", fill='black')
    draw.text((50, 230), "ID: 1234567890", fill='black')
    draw.text((50, 260), "DOB: 01/01/1990", fill='black')
    
    img.save(filename, 'JPEG', quality=95)
    return filename

def test_face_cropping_function():
    """Test the face cropping function directly."""
    print("Testing face cropping function...")
    
    # Create a mock ID card
    mock_id_path = create_mock_id_card()
    
    try:
        # Test face cropping
        print(f"Testing face cropping on: {mock_id_path}")
        cropped_path = crop_personal_photo_from_card(mock_id_path, debugging=True)
        
        if cropped_path and os.path.exists(cropped_path):
            print(f"✓ Face cropping successful: {cropped_path}")
            return True
        else:
            print("✗ Face cropping failed")
            return False
            
    except Exception as e:
        print(f"✗ Error during face cropping: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up
        if os.path.exists(mock_id_path):
            try:
                os.remove(mock_id_path)
            except:
                pass

def test_id_translation_integration():
    """Test the ID translation function with face cropping integration."""
    print("\nTesting ID translation integration...")
    
    # Create a mock ID card
    mock_id_path = create_mock_id_card(filename="test_id_for_translation.jpg")
    
    try:
        # Import the function here to avoid circular imports
        from app import handle_id_translation
        
        print(f"Testing ID translation with face cropping on: {mock_id_path}")
        
        # Test the ID translation function
        # Note: This will likely fail at the OCR/AI translation step, but we're mainly
        # testing that the face cropping part works without errors
        try:
            result, output_path = handle_id_translation(mock_id_path, "normal", "")
            print("✓ ID translation function executed without face cropping errors")
            
            # Check if the result contains information about the image processing
            if 'word_ready_dict' in result:
                word_dict = result['word_ready_dict']
                if 'person_image_path' in word_dict:
                    image_path = word_dict['person_image_path'][0]
                    if image_path and os.path.exists(image_path):
                        print(f"✓ Person image successfully extracted: {image_path}")
                        return True
                    else:
                        print("✗ Person image path not found or file doesn't exist")
                else:
                    print("ℹ No person image in word dictionary (face detection may have failed)")
            
        except Exception as e:
            # We expect this to fail at OCR/AI steps, but face cropping should work
            error_msg = str(e).lower()
            if "face" in error_msg or "crop" in error_msg or "image" in error_msg:
                print(f"✗ Face cropping related error: {str(e)}")
                return False
            else:
                print(f"ℹ Expected error in OCR/AI processing (not face cropping): {str(e)}")
                print("✓ Face cropping integration appears to be working")
                return True
                
    except ImportError as e:
        print(f"✗ Could not import handle_id_translation: {str(e)}")
        return False
        
    finally:
        # Clean up
        if os.path.exists(mock_id_path):
            try:
                os.remove(mock_id_path)
            except:
                pass

def main():
    """Run all tests."""
    print("Testing ID translation face cropping integration...")
    print("=" * 60)
    
    # Test 1: Face cropping function directly
    test1_success = test_face_cropping_function()
    
    # Test 2: ID translation integration
    test2_success = test_id_translation_integration()
    
    print("\n" + "=" * 60)
    print("Test Results:")
    print(f"Face cropping function: {'✓ PASS' if test1_success else '✗ FAIL'}")
    print(f"ID translation integration: {'✓ PASS' if test2_success else '✗ FAIL'}")
    
    if test1_success and test2_success:
        print("\n✅ All tests passed! Face cropping integration is working correctly.")
        return True
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
