#!/usr/bin/env python3
"""
Test script to verify the Vision API optimization works correctly.
This script tests the new optimized card detection and classification flow.
"""

import os
import sys
from helpers import crop_and_classify_images, clear_session_ocr_cache

def test_optimization():
    """Test the optimized card detection flow"""
    
    # Test with images from test_files folder
    test_images = [
        "test_files/ID1.png",
        "test_files/ID2.png", 
        "test_files/Family Card Single Page.png"
    ]
    
    # Filter to only existing files
    existing_images = [img for img in test_images if os.path.exists(img)]
    
    if not existing_images:
        print("No test images found in test_files folder")
        return
    
    print(f"Testing optimization with {len(existing_images)} images:")
    for img in existing_images:
        print(f"  - {img}")
    
    # Create output directory
    output_dir = "test_output"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Test the optimized flow
        print("\n=== Testing Optimized Flow ===")
        results = crop_and_classify_images(existing_images, output_dir, debugging=True)
        
        print(f"\n=== Results ===")
        print(f"Processed {len(results)} cards:")
        for result in results:
            print(f"  - {os.path.basename(result)}")
            
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
    
    # Clean up
    try:
        import shutil
        if os.path.exists(output_dir):
            shutil.rmtree(output_dir)
        print(f"\nCleaned up test output directory")
    except Exception as e:
        print(f"Warning: Could not clean up test directory: {e}")

if __name__ == "__main__":
    test_optimization()
