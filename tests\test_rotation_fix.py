#!/usr/bin/env python3
"""
Test script to verify the rotation fix works correctly
"""

import os
import shutil
from helpers import crop_cards

def test_rotation_fix():
    """Test that the rotation fix works correctly"""
    
    test_files = [
        "test_files/test9.jpg",
        "test_files/test9 - Copy.jpg"
    ]
    
    print("🔧 Testing rotation fix...")
    print("=" * 60)
    
    for test_file in test_files:
        if not os.path.exists(test_file):
            print(f"❌ Test file not found: {test_file}")
            continue
            
        print(f"\n📁 Processing: {os.path.basename(test_file)}")
        print("-" * 40)
        
        # Create a backup
        backup_file = test_file.replace(".jpg", "_backup.jpg")
        shutil.copy2(test_file, backup_file)
        
        try:
            # Process the image
            result = crop_cards(test_file, cards_to_detect=2, debugging=False)
            
            if result and len(result) > 0:
                print(f"✅ Successfully processed - {len(result)} card(s) detected")
                for i, card_path in enumerate(result, 1):
                    if os.path.exists(card_path):
                        print(f"   📄 Card {i}: {os.path.basename(card_path)}")
                    else:
                        print(f"   ❌ Card {i}: File not found - {card_path}")
            else:
                print(f"❌ No cards detected")
                
        except Exception as e:
            print(f"❌ Error processing {test_file}: {e}")
        
        finally:
            # Restore original file
            if os.path.exists(backup_file):
                shutil.copy2(backup_file, test_file)
                os.remove(backup_file)
    
    print(f"\n🎉 Rotation fix test completed!")
    print("=" * 60)

if __name__ == "__main__":
    test_rotation_fix()
